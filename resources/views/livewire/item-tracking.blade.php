<div class="card">
    <div class="card-body">
        <p class="mb-2">Upload a JSON file to track item transactions</p>

        <form wire:submit.prevent="processFile">
            <input type="file" wire:model="file" accept="application/json" class="form-control mb-2">

            @error('file')
            <div class="text-danger">{{ $message }}</div>
            @enderror

            <span class="text-muted">Max post size: {{ ini_get('post_max_size') }} | Max upload size: {{ ini_get('upload_max_filesize') }}</span>
        </form>

        <!-- Loading Animation -->
        <div wire:loading wire:target="file" class="mt-3">
            <div class="alert alert-info">Uploading file...</div>
        </div>

        @if(session()->has('error'))
            <div class="alert alert-danger mt-3">
                {{ session('error') }}
            </div>
        @endif

        @if(!empty($chestData))
            <hr>
            <h3 class="mt-3">Item Tracking Results</h3>
            <div class="table-responsive">
                <table class="table table-bordered table-striped">
                    <thead>
                    <tr>
                        <th>Chest Name</th>
                        <th>Item ID</th>
                        <th>Item Name</th>
                        <th>Put In</th>
                        <th>Taken Out</th>
                        <th>Final Total</th>
                    </tr>
                    </thead>
                    <tbody>
                    @foreach($chestData as $chestName => $items)
                        @foreach($items as $itemID => $data)
                            @php
                                $put = $data['put'] ?? 0;
                                $took = $data['took'] ?? 0;
                                $finalTotal = $put - $took;
                            @endphp
                            <tr>
                                <td>{{ $chestName }}</td>
                                <td>{{ $itemID }}</td> <!-- Display Item ID -->
                                <td>{{ $data['item_name'] }}</td> <!-- Use item_name from data -->
                                <td class="text-success fw-bold">{{ $put }}</td>
                                <td class="text-danger fw-bold">{{ $took }}</td>
                                <td class="fw-bold text-success">{{ $finalTotal }}</td>
                            </tr>
                        @endforeach
                    @endforeach
                    </tbody>
                </table>
            </div>
        @else
            <div class="alert alert-warning mt-3">
                No data available. Please upload a valid JSON file.
            </div>
        @endif
    </div>
</div>
