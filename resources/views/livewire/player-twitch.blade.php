<div class="card">
    <h5 class="card-header">
        Twitch Account
    </h5>

    <div class="card-body">
        <form wire:submit.prevent="save">
            <div class="form-group row mb-2">
                <label class="col-sm-4 col-form-label">Twitch Username</label>
                <div class="col-sm-8">
                    <input type="text" class="form-control" wire:model.defer="twitch_username">
                    @if($errors->has('twitch_username'))
                        <small class="form-text text-danger">{{ $errors->first('twitch_username') }}</small>
                    @endif
                </div>
            </div>

            <div class="form-group row mb-2">
                <div class="col-sm-8 offset-sm-4">
                    <button type="submit" class="btn btn-primary">Submit</button>
                </div>
            </div>
        </form>
    </div>
</div>
