@push('css')
    <style>
        tr.restricted td {
            border-top: 1px solid #be5504 !important;
            border-bottom: 1px solid #be5504 !important;
        }

        tr.restricted td:nth-child(1) {
            border-left: 1px solid #be5504;
        }

        tr.restricted td:nth-child(3) {
            border-right: 1px solid #be5504;
        }
    </style>
@endpush

<div class="card notes-card">
    <h5 class="card-header">
        Player Notes
    </h5>

    <div class="card-body">
        @if(auth()->user()->id == $player->id && auth()->user()->staffLevel < 5)
            <span>You cannot view your own notes</span>
        @else
            <table class="table table-bordered table-striped wrap responsive" id="notesTable{{ $_instance->id }}{{ app('lifecycle_id') }}">
                <thead>
                <th>Timestamp</th>
                <th>Note</th>
                <th>Admin</th>
                </thead>

                <tbody>
                @foreach($player->panelNotes as $note)
                    @if(isset($note->level) && auth()->user()->staffLevel < $note->level)
                        @continue
                    @endif

                    <tr class="{{ isset($note->level) ? 'restricted' : '' }}">
                        @if($note->deletable)
                            <td>{{ $note->timestamp }} {{ isset($note->level) ? '[' . config('blrp.staffLevels')[$note->level] . '+] ' : '' }}<button onclick="confirm('Are you sure you want to delete this note?') || event.stopImmediatePropagation()" wire:click="delete('{{ $note->id }}')" class="btn btn-outline-danger btn-xs" type="submit"><i class="far fa-trash-alt fa-fw"></i></button></td>
                        @else
                            <td>{{ $note->timestamp }}</td>
                        @endif

                        <td>{!! $note->body !!}</td>
                        <td>{{ $note->author?->displayName ?? $note->added_by }}</td>
                    </tr>
                @endforeach
                </tbody>
            </table>

            <form class="mt-2" wire:submit.prevent="addNote">
                <textarea class="form-control mb-1" rows="2" placeholder="Add a note..." wire:model.defer="note"></textarea>

                @if(auth()->user()->staffLevel >= 4)
                    <select class="form-control mb-1" wire:model.defer="level">
                        <option value="-1">Visible by all staff</option>
                        @foreach(config('blrp.staffLevels') as $level => $name)
                            @if($level <= auth()->user()->staffLevel && $level >= 4)
                                <option value="{{ $level }}">Visible by {{ $name }}+</option>
                            @endif
                        @endforeach
                    </select>
                @endif

                <button class="btn btn-primary btn-sm mb-1 w-100" type="submit">Add</button>

                @if($errors->has('note'))
                    <small class="form-text text-danger">{{ $errors->first('note') }}</small>
                @endif
            </form>
        @endif
    </div>
</div>

@push('js')
    <script>
        $(document).ready(function() {
            Livewire.emit('datatableInit', 'notesTable', 'notesTable{{ $_instance->id }}{{ app('lifecycle_id') }}')
        })
    </script>
@endpush
