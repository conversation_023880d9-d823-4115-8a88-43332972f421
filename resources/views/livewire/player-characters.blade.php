<div>
    @if(count($player->characters) > 0)
        <ul class="nav nav-tabs" id="myTab" role="tablist">
            @foreach($player->characters as $character)
                <li class="nav-item" role="presentation">
                    <a class="nav-link {{ $loop->first ? 'active' : '' }}" id="home-tab" data-toggle="tab" href="#character-{{ $loop->index }}" role="tab" aria-controls="home" aria-selected="true">{{ $character->firstname }} {{ $character->lastname }}{{ $character->isDeletedWord }}</a>
                </li>
            @endforeach
        </ul>

        <div class="tab-content">
            @foreach($player->characters as $character)
                <livewire:player-character :character="$character" :player="$player" :index="$loop->index" />
            @endforeach
        </div>
    @else
        <div class="alert alert-info">
            <i class="fal fa-info-square fa-fw"></i> This users does not have any characters
        </div>
    @endif
</div>
