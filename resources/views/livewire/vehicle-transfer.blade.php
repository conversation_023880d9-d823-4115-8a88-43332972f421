<div class="card">
    <div class="card-body">
        @can('game-actions')
            <p class="mb-2">Fill out the fields below to transfer a vehicle. Relevant information will be displayed for verification prior to transfer</p>

            <form class="form-inline">
                <input type="text" class="form-control mb-2 mr-sm-2" id="vehicle-plate" placeholder="Vehicle Registration (Plate)" wire:model.lazy="vehicle_plate">

                <input type="text" class="form-control mb-2 mr-sm-2" id="character-id" placeholder="Destination Character ID" wire:model.lazy="character_id">

                <select class="form-control mb-2 mr-sm-2" id="business-select" wire:model.lazy="business_id">
                    <option value="">Select Business</option>
                    @foreach($businesses as $business)
                        <option value="{{ $business->name }}">{{ $business->name }}</option>
                    @endforeach
                </select>

                <button type="button" class="btn btn-primary mb-2" wire:click="transfer">Transfer Vehicle</button>
            </form>

            @if($vehicle)
                <p class="font-weight-bold">Vehicle Information</p>

                <p class="mb-0">Plate: {{ $vehicle->registration }}</p>
                <p>Model: {{ $vehicle->vehicle }}</p>

                <p class="font-weight-bold">Registered Owner</p>

                @if($vehicle->character)
                    <p class="font-weight-bold">Registered Owner</p>

                    <p class="mb-0">Character ID: {{ $vehicle->character->id }}</p>
                    <p class="mb-0">Name: {{ $vehicle->character->full_name }}</p>
                @elseif($vehicle->business)
                    <p class="font-weight-bold">Business Owner</p>

                    <p class="mb-0">Business: {{ $vehicle->business }}</p>
                @else
                    <p class="font-weight-bold">No Registered Owner</p>
                @endif
            @endif

            @if($character)
                <hr />

                <p class="font-weight-bold">Destination Character Information</p>

                <p class="mb-0">Character ID: {{ $character->id }}</p>
                <p class="mb-0">Name: {{ $character->full_name }}</p>
            @endif
        @else
            <h3>You do not have a VRP ID linked to your panel account and cannot use the queue functions. Contact a dev to link your account.</h3>
        @endcan
    </div>
</div>
