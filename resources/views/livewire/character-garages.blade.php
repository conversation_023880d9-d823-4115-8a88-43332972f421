<div class="card">
    <h5 class="card-header">
        Reset Character Garages
    </h5>

    <div class="card-body">
        <small>Use this to reset all vehicles at a specific garage for this character</small><br /><br />

        @foreach($this->garages as $garage)
            @php
                $garage_id = is_object($garage) ? $garage->id : (isset($garage['id']) ? $garage['id'] : null);
            @endphp


            <button type="button" class="btn btn-outline-info btn-xs"
                    wire:click="resetCharacterGarage('{{ $garage_id }}', '{{ $this->character->id }}')">
                {{ $garage_id }}{{ !empty($garage->type) ? " ({$garage->type})" : '' }}
            </button>&nbsp;
        @endforeach
    </div>

</div>