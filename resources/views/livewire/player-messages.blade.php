<div class="card notes-card">
    <h5 class="card-header">
        Player DMs & Reports
    </h5>

    <div class="card-body">
        <table class="table table-bordered table-striped wrap responsive" id="characterMessagesTable{{ $_instance->id }}{{ app('lifecycle_id') }}">
            <thead>
            <tr>
                <th>From</th>
                <th>To</th>
                <th>Message</th>
                <th>Time</th>
            </tr>
            </thead>
            <tbody>
            @foreach($messages as $message)
                <tr>
                    <td>{{ $message->from }}</td>
                    <td>{{ $message->to }}</td>
                    <td>{{ $message->message }}</td>
                    <td>{{ $message->timestamp }}</td>
                </tr>
            @endforeach
            </tbody>
        </table>
    </div>
</div>

@push('js')
    <script>
        $(document).ready(function() {
            Livewire.emit('datatableInit', 'characterMessagesTable', 'characterMessagesTable{{ $_instance->id }}{{ app('lifecycle_id') }}')
        })
    </script>
@endpush
