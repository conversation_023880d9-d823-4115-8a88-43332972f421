<div class="card">
    <h5 class="card-header">
        Business Delete
    </h5>

    <div class="card-body">
        <form wire:submit.prevent="deleteBusiness">
            <div class="form-group row mb-2">
                <label class="col-sm-4 col-form-label">Confirmation</label>
                <div class="col-sm-8">
                    <input type="text" class="form-control" wire:model.defer="deleteBusinessConfirmation">
                    <small class="form-text">In order to delete this business, type "confirm delete {{ $business->id }}" in the box above and click delete business</small>
                </div>
            </div>

            <div class="form-group row mb-2">
                <div class="col-sm-8 offset-sm-4">
                    <button type="button" class="btn btn-danger" wire:click.prevent="deleteBusiness">Delete Business</button>
                </div>
            </div>
        </form>
    </div>
</div>
