<div class="card">
    <h5 class="card-header">
        Account Identifiers
    </h5>

    <div class="card-body">
        @foreach($player->identifiers as $identifier)
            <div class="form-group row mb-2">
                <label class="col-sm-4 col-form-label">{{ $identifier->real_key == 'steam' ? 'Steam (hex)' : ucfirst($identifier->real_key) }}</label>
                <div class="col-sm-8">
                    <input type="text" class="form-control" value="{{ $identifier->real_value }}" readonly>
                    @if($identifier->real_key == 'discord')
                        <small class="form-text">Look up at <a target="_blank" href="https://discord.id/">https://discord.id/</a></small>
                    @endif
                </div>
            </div>

            @if($identifier->real_key == 'steam')
                <div class="form-group row mb-2">
                    <label class="col-sm-4 col-form-label">Steam (dec)</label>
                    <div class="col-sm-8">
                        <input type="text" class="form-control" value="{{ hexdec($identifier->real_value) }}" readonly>

                        <small class="form-text">
                            <span>Look up: </span>
                            <a href="https://steamid.io/lookup/{{ hexdec($identifier->real_value) }}" target="_blank">steamid.io</a>
                            <a href="https://steamdb.info/calculator/{{ hexdec($identifier->real_value) }}" target="_blank">steamdb.info</a>
                        </small>
                    </div>
                </div>
            @endif
        @endforeach
    </div>
</div>
