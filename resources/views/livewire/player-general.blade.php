<div class="card">
    <h5 class="card-header">
        Account Information
    </h5>

    <div class="card-body">
        <form wire:submit.prevent="save">
            <div class="form-group row mb-2">
                <label class="col-sm-4 col-form-label">VRP ID</label>
                <div class="col-sm-8">
                    <input type="text" class="form-control" value="{{ $player->id }}" readonly>
                </div>
            </div>

            @can('staff-actions')
                <div class="form-group row mb-2">
                    <label class="col-sm-4 col-form-label">Flagged</label>
                    <div class="input-group col-sm-8">
                        <div class="input-group-prepend">
                            <div class="input-group-text">
                                <input type="checkbox" wire:model.defer="player.flagged">
                            </div>
                        </div>

                        <input type="text" class="form-control" disabled>
                    </div>
                </div>

                <div class="form-group row mb-2">
                    <label class="col-sm-4 col-form-label">Flag Reason</label>
                    <div class="col-sm-8 input-group">
                        <textarea class="form-control" rows="2" placeholder="Flag Reason" wire:model.defer="player.flagreason"></textarea>
                    </div>
                </div>

                <div class="form-group row mb-2">
                    <label class="col-sm-4 col-form-label">Eligibility</label>
                    <div class="input-group col-sm-8">
                        <div class="input-group-prepend">
                            <div class="input-group-text">
                                <input type="checkbox" wire:model.defer="player.steam_check_bypass" {{ auth()->user()->can('admin-actions') ? '' : 'disabled' }}>
                            </div>
                        </div>

                        <input type="text" class="form-control" disabled>
                    </div>
                </div>

                <div class="form-group row mb-2">
                    <label class="col-sm-4 col-form-label">Staff</label>
                    <div class="input-group col-sm-8">
                        <div class="input-group-prepend">
                            <div class="input-group-text">
                                <input type="checkbox" wire:model.defer="player.staff" {{ auth()->user()->can('staff-manage') ? '' : 'disabled' }}>
                            </div>
                        </div>

                        <select class="custom-select" wire:model.defer="player.staffLevel" {{ auth()->user()->can('staff-manage') ? '' : 'disabled' }}>
                            @foreach(config('blrp.staffLevels') as $level => $level_name)
                                @if($level != 3 && auth()->user()->highestFactionRanks['staff'] >= 4 && $level < auth()->user()->highestFactionRanks['staff'])
                                    <option value="{{ $level }}">{{ $level_name }}</option>
                                @elseif($level != 3)
                                    <option value="{{ $level }}" disabled>{{ $level_name }}</option>
                                @endif
                            @endforeach
                        </select>

                        @if($errors->has('player.staffLevel'))
                            <small class="form-text text-danger">{{ $errors->first('player.staffLevel') }}</small>
                        @endif
                    </div>
                </div>

                {{-- Staff Name --}}
                <div class="form-group row mb-2">
                    <label class="col-sm-4 col-form-label">Staff Name</label>
                    <div class="input-group col-sm-8">
                        <input type="text" class="form-control" wire:model.defer="player.staffName"
                               value="{{ old('player.staffName', $player->staffName) }}"
                                {{ auth()->user()->can('staff-manage') ? '' : 'disabled' }}>

                        @error('player.staffName')
                        <small class="form-text text-danger">{{ $message }}</small>
                        @enderror
                    </div>
                </div>

                {{-- Staff Title (with hover hint) --}}
                <div class="form-group row mb-2">
                    <label class="col-sm-4 col-form-label" title="Use a slash ( / ) to separate main title from subtitle.">Staff Title</label>
                    <div class="input-group col-sm-8">
                        <input type="text" class="form-control" wire:model.defer="player.staffTitle"
                               value="{{ old('player.staffTitle', $player->staffTitle) }}"
                               placeholder="e.g. Senior Developer / Script Implementation"
                                {{ auth()->user()->can('staff-manage') ? '' : 'disabled' }}>

                        @error('player.staffTitle')
                        <small class="form-text text-danger">{{ $message }}</small>
                        @enderror
                    </div>
                </div>

                @can('staff-manage')
                    {{-- Manager ID --}}
                    <div class="form-group row mb-2">
                        <label class="col-sm-4 col-form-label">Manager VRP ID</label>
                        <div class="input-group col-sm-8">
                            <input type="number" class="form-control" wire:model.defer="player.manager_id"
                                   value="{{ old('player.manager_id', $player->manager_id) }}"
                                   placeholder="User ID of manager">

                            @error('player.manager_id')
                            <small class="form-text text-danger">{{ $message }}</small>
                            @enderror
                        </div>
                    </div>
                @endcan


                <div class="form-group row mb-2">
                    <label class="col-sm-4 col-form-label">Last Login</label>
                    <div class="col-sm-8">
                        <input type="text" class="form-control" value="{{ $player->last_login }}" readonly>
                    </div>
                </div>
            @endcan

            @can('admin-actions')
                <div class="form-group row mb-2">
                    <label class="col-sm-4 col-form-label">Splunk Access</label>
                    <div class="input-group col-sm-8">
                        <div class="input-group-prepend">
                            <div class="input-group-text">
                                <input type="checkbox" wire:model.defer="player.splunk" {{ auth()->user()->can('senior-actions') || $player->staffLevel >= 4 ? '' : 'disabled' }}>
                            </div>
                        </div>

                        <input type="text" class="form-control" disabled>
                    </div>
                </div>
            @endcan

            <hr />

            <div class="badge badge-light text-md mx-auto d-block mb-3">Server Whitelisting and Highest Faction Rank</div>

            <div class="form-group row mb-2">
                <label class="col-sm-4 col-form-label">Server</label>
                <div class="input-group col-sm-8">
                    <div class="input-group-prepend">
                        <div class="input-group-text">
                            <input type="checkbox" wire:model.defer="player.whitelisted" {{ auth()->user()->can('staff-actions') ? '' : 'disabled' }}>
                        </div>
                    </div>

                    <input type="text" class="form-control" disabled>
                </div>
            </div>

            @foreach(config('blrp.factions_new') as $faction_short => $faction_name)
                <div class="form-group row mb-2">
                    <label class="col-sm-4 col-form-label">{{ $faction_name }}</label>
                    <div class="col-sm-8">
                        <input class="form-control" type="text" value="{{ config('blrp.' . $faction_short . 'Levels')[$highestFactionRanks[$faction_short]] ?? 'Not Whitelisted' }}" readonly />
                    </div>
                </div>
            @endforeach

            @can('staff-actions')
            <div class="form-group row mb-2">
                <div class="col-sm-8 offset-sm-4">
                    <button type="submit" class="btn btn-primary">Submit</button>
                </div>
            </div>
            @endcan
        </form>
    </div>
</div>
