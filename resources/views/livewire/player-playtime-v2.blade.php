<div class="card">
    <h5 class="card-header">
        Playtime (minutes)
    </h5>

    <div class="card-body">

        <!-- Character Filter Dropdown -->
        <div class="form-group">
            <label for="characterFilter">Filter by Character:</label>
            <select class="form-control" id="characterFilter">
                <option value="">All Characters</option>
            </select>
        </div>

        @foreach($columns as $index => $column)
            @if($column != 'Total')
                <div class="form-check form-check-inline">
                    <label class="form-check-label">
                        <input type="checkbox" class="form-check-input column-toggle" data-column="{{ $index + 2 }}"> {{ $column }}
                    </label>
                </div>
            @endif
        @endforeach

        <table class="table table-bordered table-striped wrap responsive" id="playtimeTable">
            <thead>
            <th>Date</th>
            <th>Character</th>
            @foreach($columns as $column)
                @if($column != 'Total')
                    <th>{{ $column }}</th>
                @endif
            @endforeach
            <th>Total</th>
            </thead>

            <tbody>
            <!-- DataTable will populate this -->
            </tbody>
        </table>
    </div>
</div>

@push('js')
    <script>
        $(document).ready(function() {
            // Fetch unique names for dropdown
            $.get('{!! url('datatable/user-playtime-names/' . $player->id) !!}', function(names) {
                let $characterFilter = $('#characterFilter');

                // Populate dropdown with names
                names.forEach(function(name) {
                    $characterFilter.append(`<option value="${name}">${name}</option>`);
                });
            });

            // Initialize the DataTable
            let table = $('#playtimeTable').DataTable({
                bInfo: true,
                bFilter: false,
                bLengthChange: false,
                bAutoWidth: false,
                scrollX: true,
                processing: true,
                serverSide: true,
                ajax: {
                url: '/datatable/user-playtime-v2/{{ $player->id }}',
                    data: function (d) {
                        return {
                        draw: d.draw,
                        start: d.start,
                        length: d.length,
                        search: d.search.value,
                        character: $('#characterFilter').val(),

                        // ✅ Send custom sort fields
                        order_col: 'date',
                        order_dir: 'desc'
                        };
                    }
                },
                order: [
                    [0, 'desc']
                ],
                columns: [
                    { data: 'date', visible: true },
                    { data: 'name', visible: true },
                    @foreach($columns as $column)
                        @if($column != 'Total')
                    { data: '{{ $column }}', visible: false },
                        @endif
                    @endforeach
                    { data: 'Total', visible: true }
                ],
                columnDefs: []
            });



            // Filter by character using DataTables custom search
            $('#characterFilter').on('change', function() {
                table.draw(); // Redraw the table with the new filter
            });

            // Toggle columns based on checkboxes
            $('.column-toggle').on('change', function() {
                let column = table.column($(this).data('column'));
                column.visible(!column.visible());
                table.columns.adjust(); // Refresh column widths
            });

            // Adjust DataTable columns on tab click
            $('#playtime-v2-tab').on('click', function() {
                setTimeout(function(){
                    table.columns.adjust();
                }, 200);
            });
        });
    </script>
@endpush
