<div class="card">
    <div class="card-body">
        @can('game-actions')
            <p class="mb-2">Fill out the fields below to add a vehicle</p>

            <form class="form-inline">
                <input type="text" class="form-control mb-2 mr-sm-2" id="vehicle-model" placeholder="Vehicle Model" wire:model.lazy="vehicle_model">

                <input type="text" class="form-control mb-2 mr-sm-2" id="character-id" placeholder="Destination Character ID" wire:model.lazy="character_id">

                <select class="form-control mb-2 mr-sm-2" id="business-select" wire:model.lazy="business_id">
                    <option value="">Select Business</option>
                    @foreach($businesses as $business)
                        <option value="{{ $business->name }}">{{ $business->name }}</option>
                    @endforeach
                </select>

                <input type="number" class="form-control mb-2 mr-sm-2" id="quantity" placeholder="Quantity" wire:model.lazy="quantity">

                <button type="button" class="btn btn-primary mb-2" wire:click="addvehicle">Add Vehicle</button>
            </form>

            @if($character)
                <hr />

                <p class="font-weight-bold">Destination Character Information</p>

                <p class="mb-0">Character ID: {{ $character->id }}</p>
                <p class="mb-0">Name: {{ $character->full_name }}</p>
            @endif
        @else
            <h3>You do not have a VRP ID linked to your panel account and cannot use the queue functions. Contact a dev to link your account.</h3>
        @endcan
    </div>
</div>
