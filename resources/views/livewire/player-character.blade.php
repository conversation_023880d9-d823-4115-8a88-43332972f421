<div class="tab-pane fade {{ $index == 0 ? 'show active' : '' }}" id="character-{{ $index }}" role="tabpanel">
    <div class="card" style="box-shadow: none;border: 1px solid #dee2e6;border-top:none">
        <div class="card-body">
            <div class="card">
                <h5 class="card-header">
                    Character Information
                </h5>

                <div class="card-body">
                    <form wire:submit.prevent="save">
                        <div class="form-group row mb-2">
                            <label class="col-sm-4 col-form-label">Character ID</label>
                            <div class="col-sm-8">
                                <input type="text" class="form-control" wire:model.defer="character.id" readonly>
                            </div>
                        </div>

                        <div class="form-group row mb-2">
                            <label class="col-sm-4 col-form-label">First Name</label>
                            <div class="col-sm-8">
                                <input type="text" class="form-control" wire:model.defer="character.firstname" {{ auth()->user()->can('change-name') ? '' : 'readonly' }}>
                                @if($errors->has('character.firstname'))
                                    <small class="form-text text-danger">{{ $errors->first('character.firstname') }}</small>
                                @endif
                            </div>
                        </div>

                        <div class="form-group row mb-2">
                            <label class="col-sm-4 col-form-label">Last Name</label>
                            <div class="col-sm-8">
                                <input type="text" class="form-control" wire:model.defer="character.lastname" {{ auth()->user()->can('change-name') ? '' : 'readonly' }}>
                                @if($errors->has('character.lastname'))
                                    <small class="form-text text-danger">{{ $errors->first('character.lastname') }}</small>
                                @endif
                            </div>
                        </div>

                        <div class="form-group row mb-2">
                            <label class="col-sm-4 col-form-label">Age</label>
                            <div class="col-sm-8">
                                <input type="text" class="form-control" wire:model.defer="character.dateofbirth" {{ auth()->user()->can('staff-actions') ? '' : 'readonly' }}>
                                @if($errors->has('character.dateofbirth'))
                                    <small class="form-text text-danger">{{ $errors->first('character.dateofbirth') }}</small>
                                @endif
                            </div>
                        </div>

                        @can('staff-actions')
                            <div class="form-group row mb-2">
                                <label class="col-sm-4 col-form-label">Phone</label>
                                <div class="col-sm-8">
                                    <div class="input-group">
                                        <input type="text" class="form-control" wire:model.defer="character.phone" readonly>
                                        <div class="input-group-append">
                                            <a class="btn btn-outline-secondary" type="button" href="{{ url('messages/' . $character->id) }}"><i class="fa-regular fa-messages fa-fw"></i></a>
                                        </div>
                                    </div>
                                </div>
                            </div>


                            <div class="form-group row mb-2">
                                <label class="col-sm-4 col-form-label">DL Number</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" wire:model.defer="character.dlnumber" readonly>
                                </div>
                            </div>

                            @if($character->wallet > 0)
                                <div class="form-group row mb-2">
                                    <label class="col-sm-4 col-form-label">(Legacy) Cash</label>
                                    <div class="col-sm-8">
                                        <input type="text" class="form-control" wire:model.defer="character.wallet" readonly>
                                    </div>
                                </div>
                            @endif

                            @if($character->bank > 0)
                                <div class="form-group row mb-2">
                                    <label class="col-sm-4 col-form-label">(Legacy) Bank</label>
                                    <div class="col-sm-8">
                                        <input type="text" class="form-control" wire:model.defer="character.bank" readonly>
                                    </div>
                                </div>
                            @endif

                            <div class="form-group row mb-2">
                                <label class="col-sm-4 col-form-label">Cash Balance</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" value="{{ $character->cash_computed }}" readonly>
                                </div>
                            </div>
                        @endcan

                        @can('banking-basic')
                            <div class="form-group row mb-2">
                                <label class="col-sm-4 col-form-label">Bank Balance</label>
                                <div class="col-sm-8">
                                    <div class="input-group">
                                        <input type="text" class="form-control" value="{{ $character?->personalBankAccount?->balance }}" readonly>
                                        <div class="input-group-append">
                                            <a class="btn btn-outline-secondary text-white" href="{{ url('banking/' . $character?->personalBankAccount?->account_number) }}">Account #: {{ $character?->personalBankAccount?->account_number }}</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endcan

                        @if(auth()->user()->can('change-name') && $character->id != 28640)
                            <div class="form-group row mb-2">
                                <div class="col-sm-8 offset-sm-4">
                                    <button type="submit" class="btn btn-primary">Submit</button>
                                </div>
                            </div>
                        @endif
                    </form>
                </div>
            </div>

            @if(!$character->isDeleted)
                @can('banking-basic')
                    @if($character->bankAccounts->isNotEmpty())
                        <div class="card mt-3">
                            <h5 class="card-header">Associated Bank Accounts</h5>
                            <div class="card-body p-2">
                                <table class="table table-sm table-striped">
                                    <thead>
                                    <tr>
                                        <th>Account #</th>
                                        <th>Balance</th>
                                        <th>Name</th>
                                        <th>Actions</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    @foreach($character->bankAccounts as $account)
                                        <tr>
                                            <td>{{ $account->account_number }}</td>
                                            <td>{{ $account->balance_formatted }}</td>
                                            <td>{{ $account->account_name ?? 'N/A' }}</td>
                                            <td>
                                                <a href="{{ url('banking/' . $account->account_number) }}" class="btn btn-sm btn-outline-secondary">
                                                    View
                                                </a>
                                            </td>
                                        </tr>
                                    @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    @endif
                @endcan

                <div class="card">
                    <a href="{{ route('factionbonuses.manage', ['userId' => $player->id]) }}" class="btn btn-primary">
                        Manage Player Faction Bonuses
                    </a>
                </div>

                @if($character->id != 28640)
                    <div class="card">
                        <h5 class="card-header">Faction Whitelisting</h5>

                        <div class="card-body">
                            <form wire:submit.prevent="saveWhitelisting">
                                @foreach(config('blrp.factions_new') as $faction_short => $faction_name)
                                    <div class="form-group row mb-2">
                                        <label class="col-sm-4 col-form-label">{{ $faction_name }}</label>
                                        <div class="col-sm-8">
                                            <select class="custom-select" wire:model.defer="character.rank_{{ $faction_short }}">
                                                @foreach(config('blrp.' . $faction_short . 'Levels') as $level => $level_name)
                                                    <option value="{{ $level }}">{{ $level_name }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                @endforeach

                                <div class="form-group row mb-2">
                                    <div class="col-sm-8 offset-sm-4">
                                        <button type="submit" class="btn btn-primary">Submit</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    @if(empty($character->deleted_at))
                        @can('doj-actions')
                            <livewire:character-vehicles :character="$character" />
                        @endcan

                        @can('staff-actions')
                            <livewire:character-garages :character="$character" />
                        @endcan
                    @endif

                    @can('staff-actions')
                        <div class="card">
                            <h5 class="card-header">Businesses</h5>

                            <div class="card-body">
                                <table class="table table-bordered table-striped wrap responsive">
                                    <tbody>
                                    @foreach($character->businesses as $business)
                                        <tr>
                                            <td>{{ $business->name }}</td>
                                        </tr>
                                    @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        @if(false)
                            <div class="card">
                                <h5 class="card-header">
                                    Character Skin Reset
                                </h5>

                                <div class="card-body">
                                    <form wire:submit.prevent="save">
                                        <div class="form-group row mb-2">
                                            <label class="col-sm-4 col-form-label">Confirmation</label>
                                            <div class="col-sm-8">
                                                <input type="text" class="form-control" wire:model.defer="resetSkinConfirmation">
                                                <small class="form-text">In order to reset skin, type "confirm reset" in the box above and click reset skin data</small>
                                            </div>
                                        </div>

                                        <div class="form-group row mb-2">
                                            <div class="col-sm-8 offset-sm-4">
                                                <button type="button" class="btn btn-danger" wire:click.prevent="resetSkinData">Reset Skin Data</button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        @endif
                    @endcan

                    @if(auth()->user()->can('admin-actions') && !$player->staff)
                        <div class="card">
                            <h5 class="card-header">
                                Character Delete
                            </h5>

                            <div class="card-body">
                                <form wire:submit.prevent="deleteCharacter">
                                    <div class="form-group row mb-2">
                                        <label class="col-sm-4 col-form-label">Confirmation</label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control" wire:model.defer="deleteCharacterConfirmation">
                                            <small class="form-text">In order to delete this character, type "confirm delete {{ $character->id }}" in the box above and click delete character</small>
                                        </div>
                                    </div>

                                    <div class="form-group row mb-2">
                                        <div class="col-sm-8 offset-sm-4">
                                            <button type="button" class="btn btn-danger" wire:click.prevent="deleteCharacter">Delete Character</button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    @endcan
                @endif
            @endif
        </div>
    </div>
</div>
