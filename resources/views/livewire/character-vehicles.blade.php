<div class="card">
    <h5 class="card-header">
        Character Vehicles
    </h5>

    <div class="card-body">
        <table class="table table-bordered table-striped wrap responsive" id="vehiclesTableC{{ $character->id }}{{ app('lifecycle_id') }}">
            <thead>
                <th>ID</th>
                <th>Plate</th>
                <th>Model</th>
                <th>Seized</th>
                <th>Garage</th>
                <th>Actions</th>
            </thead>

            <tbody>
            </tbody>
        </table>
    </div>
</div>

@push('js')
    <script>
        $(document).ready(function() {
            Livewire.emit('datatableInit', 'vehiclesTable', 'vehiclesTableC{{ $character->id }}{{ app('lifecycle_id') }}', '{{ $character->id }}')
        });
    </script>
@endpush
