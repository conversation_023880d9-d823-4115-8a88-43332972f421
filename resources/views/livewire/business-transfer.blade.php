<div class="card">
    <h5 class="card-header">
        Business Transfer
    </h5>

    <div class="card-body">
        <form wire:submit.prevent="transferBusiness">
            <div class="form-group row mb-2">
                <label class="col-sm-4 col-form-label">Character ID</label>
                <div class="col-sm-8">
                    <input type="text" class="form-control" wire:model.lazy="transferCharacterId">
                    @if($transferCharacter)
                        Target character: {{ $transferCharacter->fullName }} ({{ $transferCharacter->identifier }} - {{ $transferCharacter->id }})
                    @endif
                </div>
            </div>

            <div class="form-group row mb-2">
                <label class="col-sm-4 col-form-label">Confirmation</label>
                <div class="col-sm-8">
                    <input type="text" class="form-control" wire:model.defer="transferBusinessConfirmation">
                    <small class="form-text">In order to transfer this business, type "confirm transfer {{ $business->id }}" in the box above and click transfer business</small>
                </div>
            </div>

            <div class="form-group row mb-2">
                <div class="col-sm-8 offset-sm-4">
                    <button type="button" class="btn btn-info" wire:click.prevent="transferBusiness">Transfer Business</button>
                </div>
            </div>
        </form>
    </div>
</div>
