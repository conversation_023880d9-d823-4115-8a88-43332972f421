<div class="card">
    <div class="card-body">
        @can('game-actions')
            <p class="mb-2">Fill out the field below to deposit/withdraw funds</p>

            <form class="form">
                <input type="text" class="form-control mb-2 mr-sm-2" id="amount" placeholder="Amount (format: 100000)" wire:model.lazy="amount" autocomplete="off">
                <input type="text" class="form-control mb-2 mr-sm-2" id="details" placeholder="Transaction Details (optional)" wire:model.lazy="details" autocomplete="off">

                <button type="button" class="btn btn-success mb-2" wire:click="doAction('deposit')">Deposit</button>
                <button type="button" class="btn btn-danger mb-2" wire:click="doAction('withdraw')">Withdraw</button>
            </form>
        @else
            <h3>You do not have a VRP ID linked to your panel account and cannot use bank actions. Contact a dev to link your account.</h3>
        @endcan
    </div>
</div>
