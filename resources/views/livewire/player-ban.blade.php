<div>
    <ul class="nav nav-tabs" id="playerActionTabs" role="tablist">
        <li class="nav-item">
            <a class="nav-link active" id="ban-tab" data-toggle="tab" href="#ban" role="tab">Ban</a>
        </li>
        <li class="nav-item">
            <a class="nav-link" id="kick-tab" data-toggle="tab" href="#kick" role="tab">Kick</a>
        </li>
    </ul>

    <div class="tab-content mt-3">
        <!-- Ban Tab -->
        <div class="tab-pane fade show active" id="ban" role="tabpanel">
            <div class="card">
                <h5 class="card-header">Ban Information</h5>
                <div class="card-body">
                    <form wire:submit.prevent="save">
                        <div class="form-group row mb-2">
                            <label class="col-sm-4 col-form-label">Banned</label>
                            <div class="input-group col-sm-8">
                                <div class="input-group-prepend">
                                    <div class="input-group-text">
                                        <input type="checkbox"
                                               wire:model.defer="player.banned" {{ !auth()->user()->can('staff-actions') ? 'disabled' : '' }}>
                                    </div>
                                </div>

                                <input type="text" class="form-control" disabled>
                            </div>
                        </div>

                        <div class="form-group row mb-2">
                            <label class="col-sm-4 col-form-label">Ban Reason</label>
                            <div class="col-sm-8">
                                <input type="text" class="form-control"
                                       wire:model.defer="player.ban_reason" {{ !auth()->user()->can('staff-actions') ? 'readonly' : '' }}>
                                @if($errors->has('player.ban_reason'))
                                    <small class="form-text text-danger">{{ $errors->first('player.ban_reason') }}</small>
                                @endif
                            </div>
                        </div>

                        <div class="form-group row mb-2">
                            <label class="col-sm-4 col-form-label">Banned By</label>
                            <div class="col-sm-8">
                                <input type="text" class="form-control"
                                       wire:model.defer="player.banned_by_admin_id" {{ !auth()->user()->can('staff-actions') ? 'readonly' : '' }}>
                                @if($errors->has('player.banned_by_admin_id'))
                                    <small class="form-text text-danger">{{ $errors->first('player.banned_by_admin_id') }}</small>
                                @endif
                            </div>
                        </div>

                        <div class="form-group row mb-2">
                            <label class="col-sm-4 col-form-label">Auto Unban (EST)</label>
                            <div class="col-sm-4">
                                <input type="date" class="form-control"
                                       wire:model.defer="date" {{ !auth()->user()->can('staff-actions') ? 'readonly' : '' }}>
                                @if($errors->has('player.ban_expires'))
                                    <small class="form-text text-danger">{{ $errors->first('player.ban_expires') }}</small>
                                @endif
                            </div>
                            <div class="col-sm-4">
                                <input type="time" class="form-control"
                                       wire:model.defer="time" {{ !auth()->user()->can('staff-actions') ? 'readonly' : '' }}>
                                @if($errors->has('player.ban_expires'))
                                    <small class="form-text text-danger">{{ $errors->first('player.ban_expires') }}</small>
                                @endif
                            </div>
                        </div>

                        @can('staff-actions')
                            <div class="form-group row mb-2">
                                <div class="col-sm-8 offset-sm-4">
                                    <button type="submit" class="btn btn-primary">Submit</button>
                                    @if($player->banned)
                                        <button type="button" class="btn btn-info" wire:click="unban">Unban</button>
                                    @endif
                                </div>
                            </div>
                        @endcan
                    </form>
                </div>
            </div>
        </div>

        <!-- Kick Tab -->
        <div class="tab-pane fade" id="kick" role="tabpanel">
            <div class="card">
                <h5 class="card-header">Kick Player</h5>
                <div class="card-body">
                    <div class="form-group row mb-2">
                        <label class="col-sm-4 col-form-label">Kick Reason</label>
                        <div class="col-sm-8">
                            <input type="text" class="form-control"
                                   wire:model.defer="kickReason" {{ !auth()->user()->can('staff-actions') ? 'readonly' : '' }}>
                            @error('kickReason')
                            <small class="form-text text-danger">{{ $message }}</small>
                            @enderror

                        </div>
                    </div>

                    @can('staff-actions')
                        <div class="form-group row mb-2">
                            <div class="col-sm-8 offset-sm-4">
                                <button type="button" class="btn btn-danger" wire:click="kickPlayer">Kick Player
                                </button>
                            </div>
                        </div>
                    @endcan
                </div>
            </div>
        </div>
    </div>
</div>