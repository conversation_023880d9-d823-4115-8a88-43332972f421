<div class="card">
    <h5 class="card-header">
        Queue Priority
    </h5>

    <div class="card-body">
        @can('game-actions')
            <form wire:submit.prevent="save">
                <div class="form-group row mb-2">
                    <label class="col-sm-4 col-form-label">Queue Priority</label>
                    <div class="col-sm-8">
                        <select class="custom-select" wire:model.defer="player.priority" {{ !auth()->user()->can('admin-actions') ? 'disabled' : '' }}>
                            <option value="-1">000 – None</option>

                            @foreach(config('blrp.priority_levels') as $level => $level_name)
                                <option value="{{ $level }}">{{ str_pad($level, 3, '0', STR_PAD_LEFT) }} – {{ $level_name }}</option>
                            @endforeach
                        </select>

                        @if($errors->has('player.priority'))
                            <small class="form-text text-danger">{{ $errors->first('player.priority') }}</small>
                        @endif
                    </div>
                </div>

                <div class="form-group row mb-2">
                    <div class="col-sm-8 offset-sm-4">
                        <button type="submit" class="btn btn-primary">Submit</button>
                    </div>
                </div>
            </form>
        @else
            <h3>You do not have a VRP ID linked to your panel account and cannot use the queue functions. Contact a dev to link your account.</h3>
        @endcan
    </div>
</div>
