<div class="card">
    <div class="card-body">
        <p class="mb-2">Search by User ID, SRC, and Date</p>

        <form class="form-inline">
            <input type="text" class="form-control mb-2 mr-sm-2" id="user-id" placeholder="User ID" wire:model.lazy="user_id">

            <input type="text" class="form-control mb-2 mr-sm-2" id="src" placeholder="SRC" wire:model.lazy="src">

            <input type="date" class="form-control mb-2 mr-sm-2" id="date" wire:model.lazy="date">

            <button type="button" class="btn btn-primary mb-2" wire:click="search">Search</button>
        </form>

        @if($searchResults != null)
            <hr />

            <p class="font-weight-bold">Search Results</p>

            <div class="table-responsive">
                <table class="table">
                    <thead>
                    <tr>
                        <th>User ID</th>
                        <th>Date</th>
                        <th>Nearby Players</th>
                    </tr>
                    </thead>
                    <tbody>
                    @foreach($searchResults as $result)
                        <tr>
                            <td>{{ $result->user_id }}</td>
                            <td>{{ $result->created_at }}</td>
                            <td>
                                @if(!empty($result->nearby))
                                    @foreach($result->nearby as $player)
                                        @if(is_array($player))
                                            SRC: {{ $player['src'] }}, Distance: {{ $player['distance'] }}, VRP: {{ $player['vrp'] }}<br>
                                        @endif
                                    @endforeach
                                @else
                                    No nearby players found.
                                @endif
                            </td>
                        </tr>
                    @endforeach
                    </tbody>
                </table>
            </div>
        @endif
    </div>
</div>
