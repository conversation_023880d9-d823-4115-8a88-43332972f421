<div class="card">
    <h5 class="card-header">
        Business Information
    </h5>

    <div class="card-body">
        <form wire:submit.prevent="save">
            <div class="form-group row mb-2">
                <label class="col-sm-4 col-form-label">Name</label>
                <div class="col-sm-8">
                    <input type="text" class="form-control" value="{{ $business->name }}" readonly>
                </div>
            </div>

            <div class="form-group row mb-2">
                <label class="col-sm-4 col-form-label">Bank Account</label>
                <div class="col-sm-8">
                    <div class="input-group">
                        <input type="text" class="form-control" value="{{ $business->bank_account_number ?? 'None' }}" readonly>

                        @if($business->bank_account_number)
                            <div class="input-group-append">
                                <a class="btn btn-outline-secondary" type="button" href="{{ url('banking/' . $business->bank_account_number) }}"><i class="fa-regular fa-arrow-right fa-fw"></i></a>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <div class="form-group row mb-2">
                <label class="col-sm-4 col-form-label">Owner</label>
                <div class="col-sm-8">
                    <div class="input-group">
                        <input type="text" class="form-control" value="{{ $business->owner->fullName }} ({{ $business->owner->identifier }} - {{ $business->owner->id }})" readonly>
                        <div class="input-group-append">
                            <a class="btn btn-outline-secondary" type="button" href="{{ url('/player/' . $business->owner->identifier) }}"><i class="fa-regular fa-user fa-fw"></i></a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-group row mb-2">
                <label class="col-sm-4 col-form-label">Type</label>
                <div class="col-sm-8">
                    <div class="input-group">
                        <input type="text" class="form-control" value="{{ $business->type ?? '' }}" readonly>
                    </div>
                </div>
            </div>

            <div class="form-group row mb-2">
                <label class="col-sm-4 col-form-label">Established Gang</label>
                <div class="input-group col-sm-8">
                    <div class="input-group-prepend">
                        <div class="input-group-text">
                            <input type="checkbox" wire:model.defer="business.established" {{ auth()->user()->can('admin-actions') ? '' : 'disabled' }}>
                        </div>
                    </div>

                    <input type="text" class="form-control" disabled>
                </div>
            </div>

            @can('admin-actions')
                <div class="form-group row mb-2">
                    <div class="col-sm-8 offset-sm-4">
                        <button type="submit" class="btn btn-primary">Submit</button>
                    </div>
                </div>
            @endcan
        </form>
        <div class="form-group row mb-2 mt-4">
            <div class="col-sm-8 offset-sm-4">
                <a href="{{ url('/report/business/' . $business->id) }}" class="btn btn-info">
                    View Playtime Report
                </a>
            </div>
        </div>

    </div>
</div>
