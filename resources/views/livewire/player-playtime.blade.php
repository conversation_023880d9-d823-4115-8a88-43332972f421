<div class="card">
    <h5 class="card-header">
        Playtime (minutes)
    </h5>

    <div class="card-body">
        <table class="table table-bordered table-striped wrap responsive" id="playtimeLegacyTable">
            <thead>
            <th>Date</th>
            <th>CIV</th>
            <th>EMS</th>
            <th>LSPD</th>
            <th>BCSO</th>
            <th>DOC</th>
            </thead>

            <tbody>

            </tbody>
        </table>
    </div>
</div>

@push('js')
    <script>
        $('#playtimeLegacyTable').DataTable({
            bInfo: false, // Remove "showing x to y of z records"
            bFilter: false, // Remove search box
            bLengthChange: false, // Remove records per page selector

            processing: true,
            serverSide: true,
            ajax: '{!! url('datatable/user-playtime/' . $player->id) !!}',
            autoWidth: false,

            order: [
                [0, 'desc']
            ],

            columns: [
                { data: 'date' },
                { data: 'civ_time_played' },
                { data: 'ems_time_played' },
                { data: 'cop_time_played' },
                { data: 'sheriff_time_played' },
                { data: 'doc_time_played' },
            ],

            columnDefs: [
                {width: "25%", targets: 0},
                {width: "10%", targets: 1},
                {width: "10%", targets: 2},
                {width: "10%", targets: 3},
                {width: "10%", targets: 4},
                {width: "10%", targets: 5},
                { className: 'text-center', targets: [1, 2, 3, 4, 5] },
            ]
        })
    </script>
@endpush
