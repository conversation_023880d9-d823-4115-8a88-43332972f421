<div class="card">
    <div class="card-body">
        <p class="mb-2">Download the file and upload below</p>

        <form>
            <input type="file" wire:model="file" accept="application/json">

            @error('file') <span class="error">{{ $message }}</span> @enderror

            <span>Max post size: {{ ini_get('post_max_size') }} | Max upload size: {{ ini_get('upload_max_filesize') }}</span>
        </form>

        <div wire:loading wire:target="file">
            <br />
            Uploading file...
        </div>

        <div wire:loading.remove wire:target="file">
            @if($file)
                <br />

                <form class="form-inline" wire:submit.prevent="calculate">
                    <div class="input-group input-group-sm mb-2 mr-sm-2">
                        <div class="input-group-prepend">
                            <div class="input-group-text">Target x</div>
                        </div>

                        <input type="text" class="form-control" wire:model.lazy="target_x">
                    </div>

                    <div class="input-group input-group-sm mb-2 mr-sm-2">
                        <div class="input-group-prepend">
                            <div class="input-group-text">y</div>
                        </div>

                        <input type="text" class="form-control" wire:model.lazy="target_y">
                    </div>

                    <div class="input-group input-group-sm mb-2 mr-sm-2">
                        <div class="input-group-prepend">
                            <div class="input-group-text">z</div>
                        </div>

                        <input type="text" class="form-control" wire:model.lazy="target_z">
                    </div>

                    <div class="input-group input-group-sm mb-2 mr-sm-2">
                        <div class="input-group-prepend">
                            <div class="input-group-text">Distance</div>
                        </div>

                        <input type="text" class="form-control" wire:model.lazy="target_distance">
                    </div>
                </form>

                @if($target)
                    <table class="table table-bordered table-striped table-sm table-hover">
                        <thead>
                        <tr>
                            <th>VRP</th>
                            <th>CID</th>
                            <th>Name</th>
                            <th>Distance</th>
                            <th>Coords</th>
                        </tr>
                        </thead>

                        <tbody>
                        @forelse($characters as $character)
                            <tr>
                                <td>{{ $character['identifier'] ?? 'UNK' }}</td>
                                <td>{{ $character['id'] ?? 'UNK' }}</td>
                                <td>{{ $character['firstname'] ?? 'UNK' }} {{ $character['lastname'] ?? 'UNK' }}</td>
                                <td>{{ $character['distance'] ?? 'UNK' }}</td>
                                <td>{{ $character['position'] ?? 'UNK' }}</td>
                            </tr>
                        @empty
                            <h3>Nobody found within radius</h3>
                        @endforelse
                        </tbody>
                    </table>
                @endif
            @endif
        </div>

        <hr />

        <p class="mb-2">Example of use</p>

        <p class="mb-2">In this case, 118937 has reported he was RDM'd by a group of 5 people and one of them robbed a cup from them. First, locate the event of the cup being robbed</p>
        <img src="https://live.staticflickr.com/65535/53669151408_81d69154dd_o.png" style="max-width: 1500px" class="mb-2" />

        <p class="mb-2">Next, scope the Splunk search to 10 minutes before and 10 minutes after the timestamp of the robbing event</p>
        <img src="https://live.staticflickr.com/65535/53669154073_a8cd6f1e49_o.png" style="max-width: 1500px" class="mb-2" />

        <p class="mb-2">Look for the nearest sync to the robbery event, click the datetime value, and click add to search</p>
        <img src="https://live.staticflickr.com/65535/53668947181_d254c1a795_o.png" style="max-width: 1500px" class="mb-2" />

        <p class="mb-2">Remove the original player's ID from the search so that we see sync logs for all players online at this time</p>
        <img src="https://live.staticflickr.com/65535/53668074842_aca5cefeed_o.png" style="max-width: 1500px" class="mb-2" />

        <p class="mb-2">Add the following to the end of the search to tabulate the data and rerun the search</p>
        <pre style="color: #fff;border: 1px solid gray">| table id, char_id, datetime, position.x, position.y, position.z</pre>
        <img src="https://live.staticflickr.com/65535/53669170718_ef44c8ef04_o.png" style="max-width: 1500px" class="mb-2" />

        <p class="mb-2">Click the export button, select JSON, and export</p>
        <img src="https://live.staticflickr.com/65535/53669160558_02e0987f8e_o.png" style="max-width: 1500px" class="mb-2" />
        <img src="https://live.staticflickr.com/65535/53669302059_b8baebdd9c_o.png" style="max-width: 1500px" class="mb-2" />

        <p class="mb-2">Upload the file and calculate distances</p>
    </div>
</div>
