@extends('adminlte.page')

@section('title', 'Banned Players')

@section('content_header')
    <h1>BLRP Server Administration - Banned Players</h1>
@stop

@push('css')
    <style type="text/css">
        #bansTable tbody tr {
            cursor: pointer;
        }
    </style>
@endpush

@section('content')
    <div class="card">
        <div class="card-body">
            <table class="table table-bordered table-striped table-hover table-sm wrap responsive" id="bansTable">
                <thead>
                    <th>VRP ID</th>
                    <th>Last Character</th>
                    <th>Last IP</th>
                    <th>Last Login</th>
                    <th>Ban Reason</th>
                    <th>Banned By</th>
                </thead>

                <tbody></tbody>
            </table>
        </div>
    </div>
@stop

@push('js')
    <script>
        $('#bansTable').DataTable({
            processing: true,
            serverSide: true,
            ajax: '{!! url('datatable/bans') !!}',
            autoWidth: false,
            bLengthChange: false, // Remove records per page selector
            order: [
                [0, 'asc']
            ],

            columns: [
                { data: 'id' },
                { data: null, render: function(data) {
                        if(data.current_character === null) {
                            return '';
                        }

                        return data.current_character.firstname + ' ' + data.current_character.lastname;
                    }
                },
                { data: 'latest_ip' },
                { data: 'latest_login' },
                { data: 'ban_reason' },
                { data: 'banned_by_admin_id' },
            ],

            columnDefs: [
                { width: "5%", targets: 0 },
                { width: "10%", searchable: false, targets: 1 },
                { width: "10%", targets: 2 },
                { width: "10%", targets: 3 },
                { width: "55%", targets: 4 },
                { width: "10%", targets: 5 },

                { orderable: false, targets: [1] },
            ],

            fnRowCallback: function(nRow, aData) {
                $(nRow).click(function(e) {
                    window.location.href = 'player/' + aData.id;
                });
            },
        })
    </script>
@endpush
