@extends('adminlte.page')

@section('title', 'Faction Report')

@push('css')
    <style type="text/css">
        #faction-report tbody tr {
            cursor: pointer;
        }
    </style>
@endpush

@section('content_header')
    <h1>Faction Playtime Report - {{ strtoupper($faction) }} - {{ $month }} {{ $year }}</h1>
@stop

@section('content')
    <div class="card">
        <div class="card-header">
            <div class="d-inline-block">
                <a class="btn btn-primary d-inline-block" href="/factionreport/{{ $faction }}/{{ date('Y/F', $prevMonth) }}">&laquo; {{ date('F Y', $prevMonth) }}</a>

                <button class="btn btn-secondary d-inline-block" disabled>{{ $month }} {{ $year }}</button>

                @if($nextMonth)
                    <a class="btn btn-primary d-inline-block" href="/factionreport/{{ $faction }}/{{ date('Y/F', $nextMonth) }}">{{ date('F Y', $nextMonth) }} &raquo;</a>
                @endif
            </div>
        </div>

        <div class="card-body">
            <table class="table table-bordered table-striped table-hover table-sm" id="faction-report">
                <thead>
                    <th>ID</th>
                    @if(!$isStaff)
                        <th>Name</th>
                    @endif
                    <th>Rank</th>
                    <th>Rank(I)</th>
                    @foreach($days as $day)
                        <th>{{ $day }}</th>
                    @endforeach
                    <th>Total</th>
                </thead>

                <tbody>
                    @foreach($users as $user)
                        <tr>
                            @if($isStaff)
                                <td>{{ $user->id }}</td>
                            @else
                                <td>{{ $user->id }}</td>
                                <td>{{ $user->full_name }}</td>
                            @endif
                            <td>{{ $user->rank_mapped }}</td>
                            <td>{{ $user->rank }}</td>
                            @foreach($user->timeplayed_mapped as $date => $timeplayed)
                                <td>{{ $timeplayed }}</td>
                            @endforeach
                            <td>{{ $user->time_total }}</td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
@stop

@section('js')
    <script>
        $('#faction-report').DataTable({
            autoWidth: false,
            @if($isStaff)
            order: [
                [ 2, 'desc' ],
                [ 0, 'asc' ]
            ],
            @else
            order: [
                [ 3, 'desc' ],
                [ 0, 'asc' ]
            ],
            @endif
            pageLength: 25,
            columnDefs: [
                @if($isStaff)
                    {visible: false, targets: 2},
                    {width: "5%", targets: 0},
                    {width: "10%", targets: 1},
                @else
                    {visible: false, targets: 0},
                    {visible: false, targets: 3},
                    {width: "5%", targets: 0},
                    {width: "10%", targets: 1},
                    {width: "10%", targets: 2},
                @endif

                {
                    className: 'text-center',
                    targets: [
                        @foreach($days as $day)
                            {{ $day + 2 . ',' }}
                        @endforeach
                    ]
                },
            ],

            fnRowCallback: function(nRow, aData) {
                $(nRow).click(function() {
                    window.location.href = '/player/' + aData[0];
                });
            },
        })
    </script>
@endsection
