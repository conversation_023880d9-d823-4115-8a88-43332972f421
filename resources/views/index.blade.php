@extends('adminlte.page')

@section('title', 'Dashboard')

@section('content')
    <div class="row pt-3">
        <div class="col-6">
            <x-adminlte-info-box title="Unique Users" text="{{ \App\Models\User::orderBy('id', 'DESC')->first()->id }}" />
        </div>

        @if($countQueue <= 0)
            <div class="col-6">
                <x-adminlte-info-box title="Player Count" text="{{ $countOnline < 0 ? 'Error connecting to game server' : $countOnline }}" />
            </div>
        @else
            <div class="col-3">
                <x-adminlte-info-box title="Player Count" text="{{ $countOnline < 0 ? 'Error connecting to game server' : $countOnline }}" />
            </div>

            <div class="col-3">
                <x-adminlte-info-box title="Queue Count" text="{{ $countQueue < 0 ? 'Error connecting to game server' : $countQueue }}" />
            </div>
        @endif
    </div>

    <div class="row">
        <div class="col">
            <div class="card">
                <div class="card-body">
                    <canvas id="player-count" width="400" height="125"></canvas>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col">
            <div class="card">
                <div class="card-body">
                    <canvas id="users-per-day-count" width="400" height="50"></canvas>
                </div>
            </div>
        </div>
    </div>
@stop

@section('js')
    <script>
        var context = document.getElementById('player-count').getContext('2d');
        var playerChart = new Chart(context, {
            type: 'line',
            data: {
                labels: [
                    @foreach($playerCounts as $player_count)
                        {!! '"' . $player_count->time . '",' !!}
                    @endforeach
                ],
                datasets: [
                    {
                        label: 'Online Players',
                        data: [
                            @foreach($playerCounts as $player_count)
                                {{ $player_count->count_online . ',' }}
                            @endforeach
                        ],
                        borderColor: [
                            'rgba(255, 99, 132, 1)',
                        ],
                        backgroundColor: 'rgba(0, 0, 0, 0)',
                        borderWidth: 1,
                        yAxisID: 'online',
                    },
                    {
                        label: 'Queued Players',
                        data: [
                            @foreach($playerCounts as $player_count)
                            {{ $player_count->count_queue . ',' }}
                            @endforeach
                        ],
                        borderColor: [
                            'rgba(80, 99, 255, 1)',
                        ],
                        backgroundColor: 'rgba(0, 0, 0, 0)',
                        borderWidth: 1,
                        yAxisID: 'online',
                    },
                    {
                        label: 'Active Police',
                        data: [
                            @foreach($playerCounts as $player_count)
                            {{ $player_count->count_police . ',' }}
                            @endforeach
                        ],
                        borderColor: [
                            'rgba(208, 28, 139, 1)',
                        ],
                        backgroundColor: 'rgba(0, 0, 0, 0)',
                        borderWidth: 1,
                        yAxisID: 'right',
                    },
                    {
                        label: 'Active EMS',
                        data: [
                            @foreach($playerCounts as $player_count)
                            {{ $player_count->count_emergency . ',' }}
                            @endforeach
                        ],
                        borderColor: [
                            'rgba(230, 97, 1, 1)',
                        ],
                        backgroundColor: 'rgba(0, 0, 0, 0)',
                        borderWidth: 1,
                        yAxisID: 'right',
                    },
                ]
            },
            options: {
                scales: {
                    yAxes: [
                        {
                            id: 'online',
                            ticks: {
                                beginAtZero: true,
                                min: 0,
                                max: 300,
                            }
                        },
                        {
                            id: 'right',
                            ticks: {
                                beginAtZero: true,
                                min: 0,
                                max: 40,
                            },
                            position: 'right',
                        },
                    ]
                }
            }
        });

        var context2 = document.getElementById('users-per-day-count').getContext('2d');
        var playerChart2 = new Chart(context2, {
            type: 'line',
            data: {
                labels: [
                    @foreach($usersPerDay as $date => $new_users)
                    {!! '"' . $date . '",' !!}
                    @endforeach
                ],
                datasets: [
                    {
                        label: 'Unique Users',
                        data: [
                            @foreach($uniquePerDay as $date => $unique_users)
                            {{ $unique_users . ',' }}
                            @endforeach
                        ],
                        borderColor: [
                            'rgba(255, 99, 132, 1)',
                        ],
                        backgroundColor: 'rgba(0, 0, 0, 0)',
                        borderWidth: 1,
                        yAxisID: 'left',
                    },
                    {
                        label: 'Unique Users (Whitelisted)',
                        data: [
                            @foreach($uniqueWlPerDay as $date => $unique_users)
                                {{ $unique_users . ',' }}
                                @endforeach
                        ],
                        borderColor: [
                            'rgba(253, 98, 255, 1)',
                        ],
                        backgroundColor: 'rgba(0, 0, 0, 0)',
                        borderWidth: 1,
                        yAxisID: 'left',
                    },
                    {
                        label: 'New Users',
                        data: [
                            @foreach($usersPerDay as $date => $new_users)
                                {{ $new_users . ',' }}
                                @endforeach
                        ],
                        borderColor: [
                            'rgba(80, 99, 255, 1)',
                        ],
                        backgroundColor: 'rgba(0, 0, 0, 0)',
                        borderWidth: 1,
                        yAxisID: 'right',
                    },
                    {
                        label: 'New Users, 60+ minutes playtime',
                        data: [
                            @foreach($hourPerDay as $date => $new_users_hour)
                                {{ $new_users_hour . ',' }}
                                @endforeach
                        ],
                        borderColor: [
                            'rgba(230, 97, 1, 1)',
                        ],
                        backgroundColor: 'rgba(0, 0, 0, 0)',
                        borderWidth: 1,
                        yAxisID: 'right',
                    }
                ]
            },
            options: {
                scales: {
                    yAxes: [
                        {
                            id: 'left',
                            ticks: {
                                beginAtZero: true,
                                min: 0,
                                max: 800,
                            }
                        },
                        {
                            id: 'right',
                            ticks: {
                                beginAtZero: true,
                                min: 0,
                                max: 125,
                            },
                            position: 'right',
                        },
                    ]
                }
            }
        });
    </script>
@endsection
