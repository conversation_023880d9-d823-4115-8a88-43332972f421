@extends('adminlte.page')

@section('title_prefix', $player->id . ' - BadlandsRP')
@section('title', 'Individual Player')

@section('content_header')
    <h1>
        Player Information - {{ $player->id }}
        @if($isOnline)
            <span class="badge badge-success">Online Now</span>
        @else
            <span class="badge badge-secondary">Offline</span>
        @endif
    </h1>
@stop

@section('content')
    @can('staff-actions')
        @foreach($evasionMatches as $match)
            <div class="alert alert-warning fade show">
                Possible ban evader with account:
                {!! preg_replace('/VRP: (\d+)/', '<a href="/player/$1">VRP: $1</a>', e($match)) !!}
            </div>
        @endforeach
    @endcan


    <ul class="nav nav-tabs" role="tablist">
        <li class="nav-item" role="presentation"><a class="nav-link active" id="overview-tab" data-toggle="tab" href="#overview-content" role="tab">Account Information</a></li>
        <li class="nav-item" role="presentation"><a class="nav-link" id="playtime-v2-tab" data-toggle="tab" href="#playtime-v2-content" role="tab">Playtime</a></li>
        <li class="nav-item" role="presentation"><a class="nav-link" id="playtime-legacy-tab" data-toggle="tab" href="#playtime-legacy-content" role="tab">Playtime (Legacy)</a></li>
        @can('staff-actions')
            <li class="nav-item" role="presentation"><a class="nav-link" id="ips-tab" data-toggle="tab" href="#ips-content" role="tab">IPs</a></li>
        @endcan
        @can('senior-actions')
            <li class="nav-item" role="presentation"><a class="nav-link" id="tebex-tab" data-toggle="tab" href="#tebex-content" role="tab">Tebex</a></li>
        @endcan
    </ul>

    <div class="tab-content">
        <div class="tab-pane fade show active" id="overview-content" role="tabpanel">
            <div class="row">
                <div class="col-lg-4">
                    @if($player->id != 58794 || config('app.env') == 'local')
                        <livewire:player-ban :player="$player" />
                    @endif

                    <livewire:player-general :player="$player" />

                    @can('admin-actions')
                        <livewire:player-priority :player="$player" />
                    @endcan

                    @can('staff-actions')
                        <livewire:player-twitch :player="$player" />
                        <livewire:player-identifiers :player="$player" />
                        <livewire:player-tokens :player="$player" />
                    @endcan
                </div>

                <div class="col-lg-4">
                    @can('staff-actions')
                        <livewire:player-notes :player="$player" />
                        <livewire:player-messages :player="$player" />
                    @endcan

                    <livewire:user-name-history :player="$player" />
                </div>

                <div class="col-lg">
                    <livewire:player-characters :player="$player" />
                </div>
            </div>
        </div>

        <div class="tab-pane fade" id="playtime-v2-content" role="tabpanel">
            <livewire:player-playtime-v2 :player="$player" />
        </div>

        <div class="tab-pane fade" id="playtime-legacy-content" role="tabpanel">
            <livewire:player-playtime :player="$player" />
        </div>

        @can('staff-actions')
        <div class="tab-pane fade" id="ips-content" role="tabpanel">
            <div class="card">
                <h5 class="card-header">
                    User IP Addresses
                </h5>

                <div class="card-body">
                    <table class="table table-bordered table-striped wrap responsive" id="tebexPurchasesTable">
                        <thead>
                            <th></th>
                            <th>IP</th>
                            <th>First Seen</th>
                            <th>Last Seen</th>
                            <th>Country</th>
                            <th>City</th>
                            <th>Region</th>
                            <th>Flags</th>
                            <th>ISP</th>
                        </thead>

                        <tbody>
                        @forelse($player->ips as $ip)
                            <tr>
                                <td><a class="btn btn-info btn-sm" href="/ip/{{ $ip->id }}">More Info</a></td>
                                <td>{{ $ip->ip }}</td>
                                <td>{{ $ip->first_seen }}</td>
                                <td>{{ $ip->last_seen }}</td>
                                <td>{{ $ip->country }}</td>
                                <td>{{ $ip->city }}</td>
                                <td>{{ $ip->region }}</td>
                                <td>{{ $ip->flags }}</td>
                                <td>{{ $ip->company }}</td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="5" style="text-align: center">No data available for table</td>
                            </tr>
                        @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        @endcan

        @can('senior-actions')
        <div class="tab-pane fade" id="tebex-content" role="tabpanel">
            <div class="card">
                <h5 class="card-header">
                    Tebex Purchases
                </h5>

                <div class="card-body">
                    <table class="table table-bordered table-striped wrap responsive" id="tebexPurchasesTable">
                        <thead>
                            <th>ID</th>
                            <th>Email</th>
                            <th>IP</th>
                            <th>Package</th>
                            <th>Purchased at</th>
                        </thead>

                        <tbody>
                            @forelse($player->purchases as $purchase)
                                <tr>
                                    <td>{{ $purchase->transaction_id }}</td>
                                    <td>{{ $purchase->email }}</td>
                                    <td>{{ $purchase->ip }}</td>
                                    <td>{{ $purchase->packageName }}</td>
                                    <td>{{ $purchase->purchased_at }}</td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="5" style="text-align: center">No data available for table</td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="card">
                <h5 class="card-header">
                    Tebex Plate Tokens
                </h5>

                <div class="card-body">
                    <table class="table table-bordered table-striped wrap responsive" id="tebexPurchasesTable">
                        <thead>
                            <th>ID</th>
                            <th>Redeemed</th>
                            <th>Purchased at</th>
                        </thead>

                        <tbody>
                            @forelse($player->plateTokens as $token)
                                <tr>
                                    <td>{{ $token->transaction_id }}</td>
                                    <td>{{ $token->plate_id ? "Yes" : "No" }}</td>
                                    <td>{{ $token->created_at }}</td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="3" style="text-align: center">No data available for table</td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        @endcan
    </div>

    @can('plates-manage')
        <div class="modal fade" id="plate-edit-modal">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Change License Plate</h5>
                    </div>
                    <div class="modal-body">
                        <form>
                            <input type="hidden" id="platechange-vid" />
                            <input type="hidden" id="platechange-cid" />

                            <div class="row mb-3">
                                <label for="platechange-vrp" class="col-sm-4 col-form-label">VRP ID</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" id="platechange-vrp" readonly>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <label for="platechange-name" class="col-sm-4 col-form-label">Name</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" id="platechange-name" readonly>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <label for="platechange-model" class="col-sm-4 col-form-label">Model</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" id="platechange-model" readonly>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <label for="platechange-currentplate" class="col-sm-4 col-form-label">Current Plate</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" id="platechange-currentplate" readonly>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <label for="platechange-newplate" class="col-sm-4 col-form-label">New Plate</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" id="platechange-newplate" maxlength="8">
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                        <button type="button" class="btn btn-primary" id="platechange-submit">Change License Plate</button>
                    </div>
                </div>
            </div>
        </div>
    @endcan
@stop

@section('js')
    <script>
        let datatableDefs = {
            'playtimeTable': function(id) {
                return $('#' + id).DataTable({
                    autoWidth: false,
                    columnDefs: [
                        {width: "20%", targets: 0},
                        {width: "60%", targets: 1},
                        {width: "20%", targets: 2},
                    ]
                })
            },

            'notesTable': function(id) {
                return $('#' + id).DataTable({
                    autoWidth: false,
                    columnDefs: [
                        {width: "10%", targets: 0},
                        {width: "60%", targets: 1},
                        {width: "30%", targets: 2},
                    ]
                })
            },

            'characterMessagesTable': function(id) {
                return $('#' + id).DataTable({
                    autoWidth: true,

                    order: [[3, 'desc']]
                })
            },

            'vehiclesTable': function(id, character_id) {
                return $('#' + id).DataTable({
                    processing: true,
                    serverSide: true,
                    ajax: '{!! url('datatable/character-vehicles') !!}/' + character_id,
                    autoWidth: false,

                    columns: [
                        { data: 'id' },
                        { data: 'registration' },
                        { data: 'vehicle' },
                        { data: 'seized' },
                        { data: 'storedGarage' },
                        { data: 'actions' },
                    ],

                    columnDefs: [
                        { visible: false, targets: 0 },
                        { width: "25%", targets: 1 },
                        { width: "35%", targets: 2 },
                        { width: "10%", targets: 3 },
                        { width: "10%", targets: 4 },
                        { width: "19%", targets: 5 },

                        { orderable: false, targets: [3, 4, 5] },
                        { className: 'text-center', targets: [1, 3, 4, 5] },
                    ]
                })
            },
        }

        Livewire.on('datatableInit', (template, id, arg1) => {
            if(datatableDefs[template] !== undefined) {
                datatableDefs[template](id, arg1);
            }
        });

        Livewire.on('openPlateEditModal', data => {
            $('#platechange-vid').val(data.id);
            $('#platechange-cid').val(data.cid);
            $('#platechange-vrp').val(data.owner_id);
            $('#platechange-name').val(data.owner_name);
            $('#platechange-model').val(data.model);
            $('#platechange-currentplate').val(data.current_plate);
            $('#platechange-newplate').val('');

            $('#plate-edit-modal').modal('show');
        });

        $("#platechange-submit").click(function() {
            $('#plate-edit-modal').modal('hide');

            Livewire.emit('processPlateChange', $('#platechange-vid').val(), $('#platechange-cid').val(), $('#platechange-newplate').val());
        });
    </script>
@endsection
