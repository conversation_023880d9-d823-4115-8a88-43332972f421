@extends('adminlte.page')

@section('title', 'Election Overview')

@section('content_header')
    <h1>BLRP Server Administration - Election Overview</h1>
@stop

@push('css')
    <style type="text/css">
        #electionsTable tbody tr {
            cursor: pointer;
        }
    </style>
@endpush

@section('content')
    <div class="card">
        <div class="card-body">
            <table class="table table-bordered table-striped table-hover table-sm wrap responsive" id="electionsTable">
                <thead>
                <th>ID</th>
                <th>Vote UID</th>
                <th>Title</th>
                <th>Description</th>
                <th>Submissions</th>
                <th>Open</th>
                <th>Action</th> <!-- New column for action -->
                </thead>

                <tbody></tbody>
            </table>
        </div>
    </div>
@stop

<!-- Bootstrap Modal Structure -->
<div class="modal fade" id="detailsModal" tabindex="-1" role="dialog" aria-labelledby="detailsModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="detailsModalLabel">Details</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="detailsModalBody">
                <!-- Content will be dynamically added here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

@push('js')
    <script>
        $('#electionsTable').DataTable({
            processing: true,
            serverSide: true,
            ajax: '{!! url('datatable/electionoverview') !!}',
            autoWidth: false,
            bLengthChange: false, // Remove records per page selector
            pageLength: 25,
            order: [
                [0, 'desc'] // Assuming ID is the first column
            ],

            columns: [
                { data: 'id' },
                { data: 'vote_uid' },
                { data: 'title' },
                { data: 'description' },
                { data: 'submissions' },
                { data: 'open', render: function(data) {
                        return data ? 'Yes' : 'No';
                    } },
                {
                    data: null, // This column does not correspond to a field in the data source
                    render: function (data, type, row) {
                        return '<button class="btn btn-outline-warning btn-xs" onclick="toggleOpen(' + row.id + ')">Toggle Active</button> <button class="btn btn-outline-success btn-xs" onclick="showDetails(' + row.id + ')">View Info</button>';
                    },
                    orderable: false, // Disable sorting for this column
                },
            ],
        });


        function showDetails(ballotId) {
            $.ajax({
                url: '/election/details/' + ballotId,
                type: 'GET',
                success: function (response) {
                    // Display details using a modal
                    $('#detailsModalLabel').text('Details');
                    $('#detailsModalBody').empty();

                    $('#detailsModalBody').append('<h5>Title: ' + response.title + '</h5>');
                    $('#detailsModalBody').append('<p>Description: ' + response.description + '</p>');

                    $.each(response.questions, function (index, question) {
                        $('#detailsModalBody').append('<h6>Question: ' + question.question + '</h6>');

                        var tableHtml = '<table class="table">';
                        tableHtml += '<thead><tr><th>Option</th><th>Submission Count</th></tr></thead>';
                        tableHtml += '<tbody>';

                        $.each(question.options, function (index, option) {
                            tableHtml += '<tr>';
                            tableHtml += '<td>' + option.option + '</td>';
                            tableHtml += '<td>' + option.submission_count + '</td>';
                            tableHtml += '</tr>';
                        });

                        tableHtml += '</tbody></table>';

                        $('#detailsModalBody').append(tableHtml);
                    });

                    $('#detailsModal').modal('show');
                },
                error: function (error) {
                    console.error('Error fetching details:', error);
                }
            });
        }

        function toggleOpen(ballotId) {
            $.ajax({
                url: '/election/toggle-open/' + ballotId,
                type: 'POST',
                data: {
                    _token: '{{ csrf_token() }}',
                },
                success: function (response) {
                    $('#electionsTable').DataTable().ajax.reload();
                },
                error: function (error) {
                    console.error('Error toggling open status:', error);
                }
            });
        }
    </script>
@endpush
