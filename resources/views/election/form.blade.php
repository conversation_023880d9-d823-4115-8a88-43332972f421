<!-- resources/views/election/form.blade.php -->
@extends('adminlte.page')

@section('title', 'Create Election')

@section('content_header')
    <h1>BLRP Server Administration - Create Election</h1>
@stop

@section('content')
    <form action="/election/create" method="post" id="electionForm">
        @csrf


        <div class="form-row mb-3">
            <div class="form-group col-md-4">
                <label for="vote_uid">Vote UID:</label>
                <input type="text" id="vote_uid" name="vote_uid" required class="form-control mb-2 mr-sm-2">
            </div>

            <div class="form-group col-md-4">
                <label for="election_name">Election Name:</label>
                <input type="text" id="election_name" name="election_name" required class="form-control mb-2 mr-sm-2">
            </div>

            <div class="form-group col-md-4">
                <label for="num_questions">Number of Questions (1-5):</label>
                <select id="num_questions" name="num_questions" onchange="addQuestions(this.value)" class="form-control mb-2 mr-sm-2">
                    @for ($i = 1; $i <= 5; $i++)
                        <option value="{{ $i }}">{{ $i }}</option>
                    @endfor
                </select>
            </div>
        </div>

        <div class="form-group">
            <label for="election_description">Election Description:</label>
            <input type="text" id="election_description" name="election_description" class="form-control mb-2 mr-sm-2">
        </div>

        <br><br>

        <div id="questionContainer" class="col-sm-8">
            {{-- Dynamic questions will be added here --}}
        </div>

        <button type="submit" class="btn btn-primary">Create Election</button>
    </form>

    <script src="https://code.jquery.com/jquery-3.6.4.min.js"></script>
    <script>
        // Ensure at least one question is displayed initially
        $(document).ready(function () {
            addQuestions(1);
        });

        function addQuestions(numQuestions) {
            var container = $('#questionContainer');
            container.empty(); // Clear previous questions

            for (var i = 1; i <= numQuestions; i++) {
                container.append(`
                <div class="row mb-3">
                    <div class="col">
                        <label for="question${i}">Question ${i}:</label>
                        <input type="text" id="question${i}" name="questions[${i}][text]" required class="form-control mr-sm-2">
                    </div>

                    <div class="col">
                        <label for="question${i}_description">Description:</label>
                        <input type="text" id="question${i}_description" name="questions[${i}][description]" class="form-control mb-2 mr-sm-2"/>
                    </div>

                    <div class="col">
                        <label for="question${i}_options">Options (comma-separated):</label>
                        <input type="text" id="question${i}_options" name="questions[${i}][options]" required class="form-control mb-2 mr-sm-2">
                    </div>

                    <div class="col">
                        <label for="question${i}_max_choices">Max Choices:</label>
                        <input type="number" id="question${i}_max_choices" name="questions[${i}][max_choices]" required min="1" class="form-control mb-2 mr-sm-2">
                    </div>
                </div>
                <br>
            `);
            }
        }
    </script>
@stop
