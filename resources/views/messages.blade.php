@extends('adminlte.page')

@section('title', 'Character Messages')

@section('content_header')
    <h1>Character Messages - {{ $character->user->id }} {{ $character->firstname }} {{ $character->lastname }} ({{ $character->phone }})</h1>
@stop

@section('content')
    <div class="card">
        <div class="card-body">
            <div class="row">
                <div class="col-6">
                    <table class="table table-bordered table-striped table-hover table-sm wrap responsive" id="messageThreadTable">
                        <thead>
                            <th>Originator Name</th>
                            <th>Originator Phone</th>
                            <th>Recipient Name</th>
                            <th>Recipient Phone</th>
                            <th>Most Recent Message</th>
                        </thead>

                        <tbody>
                        @foreach($threads as $thread)
                            <tr>
                                <td>{{ $thread->rTransmitter->fullName }}</td>
                                <td>{{ $thread->transmitter }}</td>
                                <td>{{ $thread->rReceiver?->fullName }}</td>
                                <td>{{ $thread->receiver }}</td>
                                <td>{{ $thread->time }}</td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>

                <div class="col-6">
                    <livewire:text-message-history />
                </div>
            </div>
        </div>
    </div>
@stop

@push('css')
    <style>
        #messageThreadTable tbody tr {
            cursor: pointer;
        }
    </style>
@endpush

@section('js')
    <script>
        $('#messageThreadTable').DataTable({
            pageLength: 25,

            fnRowCallback: function(nRow, aData) {
                $(nRow).click(function() {
                    console.log('messageHistory call', aData[1], aData[3]);
                    Livewire.emit('messageHistory', aData[1], aData[3]);
                });
            },
        });

        Livewire.on('messagesLoaded', uuid => {
            console.log('messages loaded, init table', uuid);

            $('#messageHistory-' + uuid).DataTable({
                pageLength: 25,
                order: [
                    [2, 'desc']
                ],

                columnDefs: [
                    { width: "35%", targets: 0 },
                    { width: "35%", targets: 1 },
                    { width: "25%", targets: 2 },
                    { width: "5%", targets: 3 },

                    { orderable: false, targets: [0, 1, 3] },
                ],
            });
        });
    </script>
@endsection

