@extends('adminlte.page')

@section('title', 'Filter Maintenance')

@section('content_header')
    <h1>BLRP Server Administration - {{ isset($filter) ? 'Edit' : 'Create' }} Name Filter</h1>
@stop

@section('content')
    <div class="row">
        <div class="col-lg-4">
            <div class="card">
                <div class="card-body">
                    <form method="post" action="/namefilters{{ isset($filter) ? '/' . $filter->id : '' }}">
                        {{ csrf_field() }}

                        @if(isset($filter))
                            <div class="form-group row mb-2">
                                <label class="col-sm-4 col-form-label">ID</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" value="{{ $filter->id }}" readonly>
                                </div>
                            </div>
                        @endif

                        <div class="form-group row mb-2">
                            <label class="col-sm-4 col-form-label">First Name</label>
                            <div class="col-sm-8">
                                <input name="firstname" type="text" class="form-control" value="{{ isset($filter) ? $filter->firstname : '' }}">
                                <small class="form-text">Leave blank to impose this filter only on last name</small>
                            </div>
                        </div>

                        <div class="form-group row mb-2">
                            <label class="col-sm-4 col-form-label">Last Name</label>
                            <div class="col-sm-8">
                                <input name="lastname" type="text" class="form-control" value="{{ isset($filter) ? $filter->lastname : '' }}">
                                <small class="form-text">Leave blank to impose this filter only on first name</small>
                            </div>
                        </div>

                        <div class="form-group row mb-2">
                            <label class="col-sm-4 col-form-label">Action</label>
                            <div class="col-sm-8">
                                <select class="form-control" name="action">
                                    <option value="reject" {{ isset($filter) && $filter->action == 'reject' ? 'selected' : '' }}>Reject</option>
                                    <option value="ban" {{ isset($filter) && $filter->action == 'ban' ? 'selected' : '' }}>Ban</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group row mb-2">
                            <div class="col-sm-8 offset-sm-4">
                                <button type="submit" class="btn btn-primary">Submit</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-8">

        </div>
    </div>
@stop

