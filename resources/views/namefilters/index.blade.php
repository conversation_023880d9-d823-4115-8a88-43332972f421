@extends('adminlte.page')

@section('title', 'Name Filters')

@section('content_header')
    <h1>BLRP Server Administration - Character Name Filters</h1>
@stop

@section('content')
    <div class="card">
        <div class="card-body">
            <a href="/namefilters/create" class="btn btn-primary">Create Filter</a>
            <br/><br/>

            <table class="table table-bordered table-striped table-hover table-sm wrap responsive">
                <thead>
                    <th>ID</th>
                    <th>First Name</th>
                    <th>Last Name</th>
                    <th>Action</th>
                    <th></th>
                </thead>

                <tbody>
                    @foreach($filters as $filter)
                        <tr>
                            <td>{{ $filter->id }}</td>
                            <td>{{ $filter->firstname ?? '--' }}</td>
                            <td>{{ $filter->lastname ?? '--' }}</td>
                            <td>{{ $filter->action }}</td>
                            <th>
                                @can('admin-actions')
                                    <a class="btn btn-info btn-xs" href="/namefilters/{{ $filter->id }}"><i class="far fa-pencil-alt fa-fw"></i> Edit</a>&nbsp;&nbsp;
                                    <a class="btn btn-danger btn-xs" onclick="return confirm('Really delete this filter?')" href="/namefilters/delete/{{ $filter->id }}"><i class="far fa-trash-alt fa-fw"></i> Delete</a>
                                @endcan
                            </th>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
@stop
