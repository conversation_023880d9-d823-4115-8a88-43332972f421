@extends('adminlte.page')

@section('title', 'Housing')

@section('content_header')
    <h1>BLRP Server Administration - Housing</h1>
@stop

@push('css')
    <style type="text/css">
        #housingTable tbody tr {
            /* cursor: pointer; */
        }
    </style>
@endpush

@section('content')
    <div class="card">
        <div class="card-body">
            <table class="table table-bordered table-striped table-hover table-sm wrap responsive" id="housingTable">
                <thead>
                    <th>Address</th>
                    <th>Owner</th>
                    <th>Owner 30 day PT</th>
                    <th>Co-owner</th>
                    <th>Co-owner 30 day PT</th>
                </thead>

                <tbody>
                    @foreach($houses as $house)
                        <tr>
                            <td>{{ $house->address }}</td>
                            <td>{{ $house->owner->fullName ?? 'Deleted' }} ({{ $house->owner_character_id }})</td>
                            <td>{{ $house->owner_pt }}</td>
                            <!-- <td>{{ $house->coowner_character_id }}</td> -->
                            <td>{{ $house->coowner?->fullName }} ({{ $house->coowner_character_id }})</td>
                            <td>{{ $house->coowner_pt }}</td>
                            <!-- <td>{{ $house->coowner ? $house->coowner_pt : '' }}</td> -->
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
@stop
