@extends('adminlte.page')

<!DOCTYPE html>
<html>
<head>
    <title>Staff Org Chart</title>
    <style>
        body {
            background-color: #121212;
        }

        #tree {
            background-color: #454d55;
            padding: 2rem;
            display: flex;
            justify-content: center;
            align-items: center;
        }
    </style>
    <div id="tree" class="mb-5"></div>

    <h4 class="text-white">📊 Staff Role Hierarchy</h4>
    <div id="staffHierarchy"></div>


    <script src="{{ asset('js/orgchart.js') }}"></script>
    <script src="{{('js/orgchart-custom-template.js') }}"></script>
    <style>
        body { margin: 0; }
        #tree { width: 100%; height: 100vh; }
    </style>
</head>
<body>
<div id="tree"></div>

<script>
  const roleHierarchy = [
    // Common root
    { id: 1001, name: 'Community Manager', title: '', subtitle: '' },

    // Admin hierarchy
    { id: 1002, pid: 1001, name: 'Senior Administrators' },
    { id: 1003, pid: 1002, name: 'Administrators' },
    { id: 1004, pid: 1003, name: 'Moderators' },
    { id: 1005, pid: 1004, name: 'Support Staff' },

    // Dev hierarchy
    { id: 1010, pid: 1001, name: 'Lead Developer' },
    { id: 1011, pid: 1010, name: 'Senior Developers' },
    { id: 1012, pid: 1011, name: 'Developers' },
    { id: 1013, pid: 1012, name: 'Asset Developers'},
    { id: 1014, pid: 1012, name: 'Contributors' }
  ];


  const dynamicNodes = @json($nodes);

  // Merge arrays
  const combinedNodes = [...dynamicNodes, ...roleHierarchy];


  const chart = new OrgChart(document.getElementById("tree"), {
    template: "noImage",
    enableSearch: false,
    nodeBinding: {
      field_0: "name",
      field_1: "title",
      field_2: "subtitle"
    },
    nodes: combinedNodes
  });

</script>
</body>
</html>
