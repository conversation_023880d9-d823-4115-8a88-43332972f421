@extends('adminlte.page')

@section('title', 'Garage Viewer')

@section('content_header')
    <h1>BLRP Server Administration - Garage Viewer</h1>
@stop

@section('content')
    <div class="card">
        <div class="card-body">
            @if($response && $response->successful())
                <table class="table table-bordered table-striped table-hover table-sm wrap responsive" id="queueTable">
                    <thead>
                        <th>ID</th>
                        <th>Name</th>
                        <th>Coordinates</th>
                    </thead>

                    <tbody>
                        @foreach($garages as $id => $garage)
                            <tr>
                                <td>{{ $id }}</td>
                                <td>{{ $garage['name'] }}</td>
                                <td>vector3({{ round($garage['coords']['x'], 3) }}, {{ round($garage['coords']['y'], 3) }}, {{ round($garage['coords']['z'], 3) }})</td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            @else
                <h3>Panel failed to connect to game server</h3>
            @endif
        </div>
    </div>
@stop

