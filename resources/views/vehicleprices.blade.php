@extends('adminlte.page')

@section('title', 'Vehicle Price Viewer')

@section('content_header')
    <h1>BLRP Server Administration - Vehicle Price Viewer</h1>
@stop

@section('content')
    <div class="card">
        <div class="card-body">
            @if($response && $response->successful())
                <table class="table table-bordered table-striped table-hover table-sm wrap responsive" id="vehiclePriceTable">
                    <thead>
                        <th>Model</th>
                        <th>Name</th>
                        <th>Price</th>
                    </thead>

                    <tbody>
                    @foreach($vehicles as $vehicle_name => $vehicle)
                        <tr>
                            <td>{{ $vehicle_name }}</td>
                            <td>{{ $vehicle[1] }}</td>
                            <td>${{ number_format($vehicle[2]) }}</td>
                        </tr>
                    @endforeach
                    </tbody>
                </table>
            @else
                <h3>Panel failed to connect to game server</h3>
            @endif
        </div>
    </div>
@stop

@push('js')
    <script>
        $('#vehiclePriceTable').DataTable({
            bLengthChange: false, // Remove records per page selector
            pageLength: 20,
            autoWidth: false,
        })
    </script>
@endpush

