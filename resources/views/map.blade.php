@extends('adminlte.page')

@section('title', 'Map')

@section('content_header')
    <h1>BLRP Server Administration - Map</h1>
@stop

@push('css')
            <style type="text/css">
                .gm-style .gm-style-iw{font-weight:300;font-size:13px;overflow:hidden}.gm-style .gm-iw{color:#2c2c2c}.gm-style .gm-iw b{font-weight:400}.gm-style .gm-iw a:link,.gm-style .gm-iw a:visited{color:#4272db;text-decoration:none}.gm-style .gm-iw a:hover{color:#4272db;text-decoration:underline}.gm-style .gm-iw .gm-title{font-weight:400;margin-bottom:1px}.gm-style .gm-iw .gm-basicinfo{line-height:18px;padding-bottom:12px}.gm-style .gm-iw .gm-website{padding-top:6px}.gm-style .gm-iw .gm-photos{padding-bottom:8px;-ms-user-select:none;-moz-user-select:none;-webkit-user-select:none}.gm-style .gm-iw .gm-sv,.gm-style .gm-iw .gm-ph{cursor:pointer;height:50px;width:100px;position:relative;overflow:hidden}.gm-style .gm-iw .gm-sv{padding-right:4px}.gm-style .gm-iw .gm-wsv{cursor:pointer;position:relative;overflow:hidden}.gm-style .gm-iw .gm-sv-label,.gm-style .gm-iw .gm-ph-label{cursor:pointer;position:absolute;bottom:6px;color:#fff;font-weight:400;text-shadow:rgba(0,0,0,0.7) 0 1px 4px;font-size:12px}.gm-style .gm-iw .gm-stars-b,.gm-style .gm-iw .gm-stars-f{height:13px;font-size:0}.gm-style .gm-iw .gm-stars-b{position:relative;background-position:0 0;width:65px;top:3px;margin:0 5px}.gm-style .gm-iw .gm-rev{line-height:20px;-ms-user-select:none;-moz-user-select:none;-webkit-user-select:none}.gm-style.gm-china .gm-iw .gm-rev{display:none}.gm-style .gm-iw .gm-numeric-rev{font-size:16px;color:#dd4b39;font-weight:400}.gm-style .gm-iw.gm-transit{margin-left:15px}.gm-style .gm-iw.gm-transit td{vertical-align:top}.gm-style .gm-iw.gm-transit .gm-time{white-space:nowrap;color:#676767;font-weight:bold}.gm-style .gm-iw.gm-transit img{width:15px;height:15px;margin:1px 5px 0 -20px;float:left}.gm-iw {text-align:left;}.gm-iw .gm-numeric-rev {float:left;}.gm-iw .gm-photos,.gm-iw .gm-rev {direction:ltr;}.gm-iw .gm-stars-f, .gm-iw .gm-stars-b {background:url("//maps.gstatic.com/mapfiles/api-3/images/review_stars.png") no-repeat;background-size: 65px 26px;float:left;}.gm-iw .gm-stars-f {background-position:left -13px;}.gm-iw .gm-sv-label,.gm-iw .gm-ph-label {left: 4px;}
            </style>
            <style type="text/css">
                .gm-style .gm-style-mtc label,.gm-style .gm-style-mtc div{font-weight:400}
            </style>
            <link type="text/css" rel="stylesheet" href="//fonts.googleapis.com/css?family=Roboto:300,400,500,700">
            <style type="text/css">
                .gm-style .gm-style-cc span,.gm-style .gm-style-cc a,.gm-style .gm-style-mtc div{font-size:10px}
            </style>
            <style type="text/css">
                @media print { .gm-style .gmnoprint, .gmnoprint { display:none }}
                @media screen { .gm-style .gmnoscreen, .gmnoscreen { display:none }}
            </style>
            <style type="text/css">
                .gm-style{font-family:Roboto,Arial,sans-serif;font-size:11px;font-weight:400;text-decoration:none}.gm-style img{max-width:none}
            </style><title>TimRP Map</title>
            <link href="/favicon.ico" rel="shortcut icon">
            <link href="//fonts.googleapis.com/css?family=Roboto:400,700" rel="stylesheet">
            <link href="//fonts.googleapis.com/css?family=Open+Sans" rel="stylesheet">
            <link href="map/style/app-a.css" rel="stylesheet" type="text/css">
            <link href="//maxcdn.bootstrapcdn.com/font-awesome/latest/css/font-awesome.min.css" rel="stylesheet">
            <script type="text/javascript" charset="UTF-8" src="//maps.googleapis.com/maps-api-v3/api/js/39/3/marker.js"></script>
            <script type="text/javascript" charset="UTF-8" src="//maps.googleapis.com/maps-api-v3/api/js/39/3/onion.js"></script>
            <script type="text/javascript" charset="UTF-8" src="//maps.googleapis.com/maps-api-v3/api/js/39/3/infowindow.js"></script>
            <script type="text/javascript" charset="UTF-8" src="//maps.googleapis.com/maps-api-v3/api/js/39/3/controls.js"></script>
            <script type="text/javascript" charset="UTF-8" src="//maps.googleapis.com/maps-api-v3/api/js/39/3/stats.js"></script>
            <script type="text/javascript" src="//ajax.googleapis.com/ajax/libs/jquery/1.10.2/jquery.min.js"></script>
            <script type="text/javascript">
                var _CDN_url = "";
                var _MAP_tileURL = "map/tiles/";
                var _MAP_iconURL = "map/icons/";
                var _MAP_atlasMap = true;
                var _MAP_satelliteMap = true;
                var _MAP_roadMap = true;
                var _MAP_UVInvMap = true;
                var _GMAP_KEY = 'AIzaSyBILpkwp1sTA-zhVY3EeYTsBZNXkGcSIqc';	// Google Maps API key

                $(document).ready(function(){$(document).foundation();});
            </script><script type="text/javascript" src="map/js/clipper.js"></script>
            <script type="text/javascript" src="map/js/compact/init.js"></script>
            <script type="text/javascript" src="map/js/compact/app.js"></script>
            <script type="text/javascript" src="map/js/compact/functions.js"></script>
            <script type="text/javascript" src="map/js/compact/objects.js"></script>
            <script type="text/javascript" src="map/js/compact/locations.js"></script>
            <script type="text/javascript" src="map/js/compact/map.js?v=2"></script><style>
                .info-icon{background-image:url('info.png') !important; }
            </style>
@endpush

@section('content')
    <div id="wrapper" style="height: 800px !important;">
        <div id="map-holder">
            <div id="map-canvas" style="position:relative; overflow:hidden; background-color:rgb(15,168,210);"></div>
        </div>
    </div>
@stop

@push('js')
    <script src="//maps.googleapis.com/maps/api/js?callback=globalInit&key=AIzaSyBILpkwp1sTA-zhVY3EeYTsBZNXkGcSIqc" async></script>
    <script type="text/javascript">
        var interval = setInterval(checkReady, 100);

        function checkReady() {
            if(MapRenderReady) {
                initMap();
            }
        }

        function initMap() {
            clearInterval(interval);

            createMarker(false, true, new MarkerObject('TEST', new Coordinates(0, 0, 0), MarkerTypes.normal, "", ""), "");

            createArea({
                name: 'Crash zone PB',
                type: {
                    borderColor: '#FF0000',
                    borderOpacity: 100,
                    borderWeight: 4.0,
                    opacity: 0,
                },
                paths: [
                    convertToMapGMAP(225.94285583496, -517.92529296875),
                    convertToMapGMAP(424.8923034668, -517.92529296875),
                    convertToMapGMAP(424.8923034668, -622.62854003906),
                    convertToMapGMAP(225.94285583496, -622.62854003906)
                ]
            })

            createArea({
                name: 'Crash zone other',
                type: {
                    borderColor: '#0000FF',
                    borderOpacity: 100,
                    borderWeight: 4.0,
                    opacity: 0,
                },
                paths: [
                    convertToMapGMAP(-3253.6088867188, 6664.3779296875),
                    convertToMapGMAP(3308.4130859375, 6664.3779296875),
                    convertToMapGMAP(3308.4130859375, -3156.6726074219),
                    convertToMapGMAP(-3253.6088867188, -3156.6726074219)
                ]
            })

            createArea({
                name: 'Streaming extents',
                type: {
                    borderColor: '#00FF00',
                    borderOpacity: 100,
                    borderWeight: 4.0,
                    opacity: 0,
                },
                paths: [
                    convertToMapGMAP(262.4139, -544.3167),
                    convertToMapGMAP(380.7115, -544.3167),
                    convertToMapGMAP(380.7115, -644.5585),
                    convertToMapGMAP(262.4139, -644.5585)
                ]
            })

            /*

            strokeColor: typeof(obj.type.borderColor) !== 'undefined' ? obj.type.borderColor : '#000000',
		strokeOpacity: typeof(obj.type.borderOpacity) !== 'undefined' ? obj.type.borderOpacity : 0,
		strokeWeight: typeof(obj.type.borderWeight) !== 'undefined' ? obj.type.borderWeight : 0,
		fillColor: typeof(obj.type.color) !== 'undefined' ? obj.type.color : '#FFF',
		fillOpacity: typeof(obj.type.opacity) !== 'undefined' ? obj.type.opacity : 0.35,

             */

            <?php
                /*
            while($row = $files->fetch_assoc()) {
                $type = "normal";

                if($row['jur'] == "SP") {
                    $type = "red";
                } elseif($row['jur'] == "PB") {
                    $type = "yellow";
                } elseif($row['jur'] == "MP") {
                    $type = "green";
                } elseif($row['jur'] == "BC") {
                    $type = "purple";
                } elseif($row['jur'] == "ZZ") {
                    $type = "orange";
                }

                echo 'createMarker(false, true, new MarkerObject("' . $row['jur'] . $row['yr'] . '-' . $row['id'] . '", new Coordinates(' . $row['x'] . ', ' . $row['y'] . ', 0), MarkerTypes.' . $type . ', "", ""), "");';
            }
                */
            ?>
        }
    </script>
@endpush
