@extends('adminlte.page')

@section('title', 'Business Playtime Report')

@section('content_header')
    <h1>Business Playtime Report - {{ strtoupper($business->name) }} - {{ $month }} {{ $year }}</h1>
@stop

@section('content')
    <div class="card">
        <div class="card-header">
            <div class="d-inline-block">
                <a class="btn btn-primary d-inline-block" href="/report/business/{{ $business->id }}/{{ date('Y/F', $prevMonth) }}">&laquo; {{ date('F Y', $prevMonth) }}</a>

                <button class="btn btn-secondary d-inline-block" disabled>{{ $month }} {{ $year }}</button>

                @if($nextMonth)
                    <a class="btn btn-primary d-inline-block" href="/report/business/{{ $business->id }}/{{ date('Y/F', $nextMonth) }}">{{ date('F Y', $nextMonth) }} &raquo;</a>
                @endif
            </div>
        </div>

        <div class="card-body">
            <table class="table table-bordered table-striped table-hover table-sm" id="business-report">
                <thead>
                    <th>ID</th>
                    <th>Name</th>
                    @foreach($days as $day)
                        <th>{{ $day }}</th>
                    @endforeach
                    <th>Total</th>
                </thead>

                <tbody>
                @foreach($characters as $char)
                    <tr>
                        <td>{{ $char->identifier }}</td>
                        <td>{{ $char->full_name ?? $char->firstname . ' ' . $char->lastname }}</td>
                        @foreach($char->timeplayed_mapped as $date => $timeplayed)
                            <td>{{ $timeplayed }}</td>
                        @endforeach
                        <td>{{ $char->time_total }}</td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
@stop

@section('js')
    <script>
        $('#business-report').DataTable({
            autoWidth: false,
            order: [
                [ {{ 2 + count($days) }}, 'desc' ],
                [ 0, 'asc' ]
            ],
            pageLength: 25,
            columnDefs: [
            {visible: true, targets: 0},
            {width: "5%", targets: 0},
            {width: "10%", targets: 1},
                {
                    className: 'text-center',
                  targets: [
                      @for($i = 0; $i < count($days); $i++)
                          {{ $i + 2 }},
                      @endfor
                      {{ 2 + count($days) }} // also center-align "Total"
                  ]
                },
            ],

            fnRowCallback: function(nRow, aData) {
                $(nRow).click(function() {
                    window.location.href = '/player/' + aData[0];
                });
            },
        })
    </script>
@endsection
