@extends('adminlte.page')

@section('title', 'Business List')

@section('content_header')
    <h1>BLRP Server Administration - Businesses</h1>
@stop

@push('css')
    <style type="text/css">
        #businessesTable tbody tr {
            //cursor: pointer;
        }
    </style>
@endpush

@section('content')
    <div class="card">
        <div class="card-body">
            <table class="table table-bordered table-striped table-hover table-sm wrap responsive" id="businessesTable">
                <thead>
                    <th>Name</th>
                    <th>Owner</th>
                    <th>Employee Count</th>
                    <th>Type</th>
                    <th>Actions</th>
                </thead>

                <tbody></tbody>
            </table>
        </div>
    </div>
@stop

@push('js')
    <script>
        $('#businessesTable').DataTable({
            processing: true,
            serverSide: true,
            ajax: '{!! url('datatable/businesses') !!}',
            autoWidth: false,
            bLengthChange: true, // Remove records per page selector
            pageLength: 25,
            order: [
                [0, 'asc']
            ],

            columns: [
                { data: 'name' },
                { data: 'owner' },
                { data: 'employees_count' },
                { data: 'type' },
                { data: 'actions' },
            ],

            columnDefs: [
                /*{ width: "5%", targets: 1 },
                { width: "5%", targets: 2 },
                { width: "20%", targets: 3 },
                { width: "25%", targets: 4 },
                { width: "25%", targets: 5 },
                { width: "5%", targets: 6 },

                { visible: false, targets: [0] },*/
            ],

            fnRowCallback: function(nRow, aData) {
                $(nRow).click(function(e) {
                    //window.location.href = 'businesses/' + aData.id;
                });
            },
        })
    </script>
@endpush
