@extends('adminlte.page')

@section('title_prefix', $business->name . ' - BadlandsRP')
@section('title', 'Individual Player')

@section('content_header')
    <h1>Business Information - {{ $business->name }}</h1>
@stop

@push('css')
    <style>
        #businessMembersTable tbody tr {
            cursor: pointer;
        }
    </style>
@endpush

@section('content')
    <div class="row">
        <!-- Left -->
        <div class="col-lg-4">
            <livewire:business-information :business="$business" />

            <div class="card notes-card">
                <h5 class="card-header">
                    Employees / Members
                </h5>

                <div class="card-body">
                    <table class="table table-bordered table-striped table-hover table-sm wrap responsive" id="businessMembersTable">
                        <thead>
                            <th>VRP ID</th>
                            <th>Character ID</th>
                            <th>Character Name</th>
                        </thead>

                        <tbody>
                            @foreach($business->employees as $employee)
                                <tr>
                                    <td>{{ $employee->identifier }}</td>
                                    <td>{{ $employee->id }}</td>
                                    <td>{{ $employee->fullName }}</td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Right -->
        <div class="col-lg-8">
            <livewire:business-notes :business="$business" />

            @can('senior-actions')
            <div class="row">
                <div class="col-6">
                    <livewire:business-transfer :business="$business" />
                </div>

                <div class="col-6">
                    <livewire:business-delete :business="$business" />
                </div>
            </div>
            @endcan
        </div>
    </div>
@stop

@section('js')
    <script>
        $('#businessMembersTable').DataTable({
            pageLength: 25,
            autoWidth: false,
            columnDefs: [
                {width: "10%", targets: 0},
                {width: "10%", targets: 1},
                {width: "40%", targets: 2},
            ],

            fnRowCallback: function(nRow, aData) {
                $(nRow).click(function() {
                    window.location.href = '/player/' + aData[0];
                });
            },
        })

        let businessDatatableDefs = {
            'businessNotesTable': function(id) {
                return $('#' + id).DataTable({
                    autoWidth: false,
                    columnDefs: [
                        {width: "10%", targets: 0},
                        {width: "75%", targets: 1},
                        {width: "15%", targets: 2},
                    ]
                })
            },
        }

        Livewire.on('businessDatatableInit', (template, id, arg1) => {
            if(businessDatatableDefs[template] !== undefined) {
                businessDatatableDefs[template](id, arg1);
            }
        });
    </script>
@endsection
