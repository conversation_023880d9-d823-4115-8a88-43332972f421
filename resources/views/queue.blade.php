@extends('adminlte.page')

@section('title', 'Queued Players')

@section('content_header')
    <h1>BLRP Server Administration - Queued Players ({{ count($queueData) }})</h1>
@stop

@section('content')
    <div class="card">
        <div class="card-body">
            @can('game-actions')
                @if($response && $response->successful())
                    <table class="table table-bordered table-striped table-hover table-sm wrap responsive" id="queueTable">
                        <thead>
                            <th>Position</th>
                            <th>VRP ID</th>
                            <th>STEAM Name</th>
                            <th>Last Played Character</th>
                            <th>Actions</th>
                        </thead>

                        <tbody>
                        @forelse($queueData as $position => $queued_player)
                            <tr>
                                <td>{{ $position + 1 }}</td>
                                <td>{{ $queued_player['vrp_id'] }}</td>
                                <td>{{ $queued_player['name'] }}</td>
                                <td>{{ $queued_player['current_character'] }}</td>
                                @if($queued_player['priority'] < 12)
                                    <td><a class="btn btn-outline-info btn-xs" onclick="return confirm('Are you sure you want to bump this user?')" href="{{ url('queue/bump/' . $queued_player['vrp_id']) }}"><i class="fa-regular fa-arrow-up fa-fw"></i> Bump</a></td>
                                @else
                                    <td>Bump Unavailable</td>
                                @endif
                            </tr>
                        @empty
                            <tr>
                                <td colspan="4" style="text-align: center">No players in the queue</td>
                            </tr>
                        @endforelse
                        </tbody>
                    </table>
                @else
                    <h3>Panel failed to connect to game server</h3>
                @endif
            @else
                <h3>You do not have a VRP ID linked to your panel account and cannot use the queue functions. Contact a dev to link your account.</h3>
            @endcan
        </div>
    </div>
@stop

