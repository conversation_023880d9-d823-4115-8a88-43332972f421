@extends('adminlte.page')

@section('title', 'Faction Bonus Report')

@section('content_header')
    <h1>Faction Bonus Report</h1>
@stop

@section('content')
    <div class="container">
        <h1>Faction Bonus Report</h1>

        <!-- Display Validation Errors -->
        @if ($errors->any())
            <div class="alert alert-danger">
                <ul>
                    @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <!-- Filter Form -->
        <form method="GET" action="{{ route('factionbonuses.report') }}" class="mb-3">
            <div class="form-group">
                <label for="faction">Filter by Faction</label>
                <select name="faction" id="faction" class="form-control">
                    <option value="">All Factions</option>
                    @foreach ($factions as $factionName => $v2Value)
                        <option value="{{ $v2Value }}" {{ request('faction') == $v2Value ? 'selected' : '' }}>
                            {{ $factionName }}
                        </option>
                    @endforeach
                </select>
            </div>
            <button type="submit" class="btn btn-primary">Filter</button>
        </form>

        <!-- Display Bonuses -->
        <table class="table">
            <thead>
            <tr>
                <th>Character Name</th>
                <th>Expiry Date</th>
                <th>Extra Pay</th>
                <th>Reason</th>
                <th>Group</th>
                <th>Actions</th>
            </tr>
            </thead>
            <tbody>
            @forelse($bonuses as $bonus)
                <tr>
                    <td>{{ $bonus['character_name'] }}</td>
                    <td>{{ $bonus['expiry_date'] ? date('Y-m-d H:i:s', $bonus['expiry_date']) : 'N/A' }}</td>
                    <td>{{ $bonus['extra_pay'] }}</td>
                    <td>{{ $bonus['reason'] }}</td>
                    <td>{{ $bonus['group'] }}</td>
                    <td>
                        <!-- View User's Faction Bonus Page -->
                        <a href="{{ route('factionbonuses.manage', ['userId' => $bonus['user_id']]) }}" class="btn btn-info">View User Bonuses</a>
                    </td>
                </tr>
            @empty
                <tr>
                    <td colspan="6">No bonuses found.</td>
                </tr>
            @endforelse
            </tbody>
        </table>
    </div>
@endsection
