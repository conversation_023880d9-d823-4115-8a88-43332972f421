@extends('adminlte.page')

@section('title', 'Online Players')

@section('content_header')
    <h1>BLRP Server Administration - Online Players</h1>
@stop

@push('css')
    <style type="text/css">
        #onlineResultsTable tbody tr {
            cursor: pointer;
        }
    </style>
@endpush

@section('content')
    <div class="card">
        <div class="card-body">
            <table class="table table-bordered table-striped table-hover table-sm wrap responsive" id="onlineResultsTable">
                <thead>
                    <th>VRP ID</th>
                    <th>Character ID</th>
                    <th>Character Name</th>
                    <th>Whitelisted</th>
                    <th>Flagged</th>
                    <th>IP Flags</th>
                </thead>

                <tbody>
                @foreach($players as $player)
                    <tr>
                        @foreach($player as $value)
                            @if(is_array($value) && isset($value['html']) && isset($value['order']))
                                <td data-order="{{ $value['order'] }}">{!! $value['html'] !!}</td>
                            @else
                                <td>{!! $value !!}</td>
                            @endif
                        @endforeach
                    </tr>
                @endforeach
                </tbody>
            </table>
        </div>
    </div>
@stop

@push('js')
    <script>
        $('#onlineResultsTable').DataTable({
            bLengthChange: false, // Remove records per page selector
            pageLength: 140,
            autoWidth: false,
            columnDefs: [
                {width: "10%", targets: 0},
                {width: "10%", targets: 1},
                {width: "40%", targets: 2},
                {width: "10%", targets: 3},
                {width: "10%", targets: 4},
                {width: "20%", targets: 5},
            ],

            fnRowCallback: function(nRow, aData) {
                $(nRow).click(function() {
                    window.location.href = 'player/' + aData[0];
                });
            },
        })
    </script>
@endpush
