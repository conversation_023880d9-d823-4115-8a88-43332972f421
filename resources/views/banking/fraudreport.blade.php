@extends('adminlte.page')

@section('title', 'Banking - Fraud Report')

@section('content')
    <div class="container">
        <h3 class="mb-4">🕵️‍♂️ Tax Fraud Flag Report</h3>

        <p class="text-muted">
            This report lists all transactions flagged with <code>POSSIBLE FRAUD DETECTED</code> on account <strong>{{ $account->account_number }}</strong>.
        </p>
        <form method="GET" class="form-inline mb-3">
            <label for="range" class="mr-2">Filter by Date Range:</label>
            <select name="range" id="range" class="form-control mr-2" onchange="this.form.submit()">
                <option value="1" {{ $range == '1' ? 'selected' : '' }}>Last 1 Month</option>
                <option value="3" {{ $range == '3' ? 'selected' : '' }}>Last 3 Months</option>
                <option value="6" {{ $range == '6' ? 'selected' : '' }}>Last 6 Months</option>
                <option value="12" {{ $range == '12' ? 'selected' : '' }}>Last 12 Months</option>
                <option value="all" {{ $range == 'all' ? 'selected' : '' }}>All Time</option>
            </select>
        </form>

        <div class="card mb-4">
            <div class="card-header bg-dark text-white">
                🚨 Suspect Summary
            </div>
            <div class="card-body p-0">

                <div class="accordion" id="fraudAccordion">
                    @foreach($fraudByPerson as $name => $transactions)
                        <div class="card bg-dark text-white mb-2">
                            <div class="card-header p-2" id="heading-{{ Str::slug($name) }}">
                                <h5 class="mb-0">
                                    <button class="btn btn-link text-white" type="button" data-toggle="collapse"
                                            data-target="#collapse-{{ Str::slug($name) }}"
                                            aria-expanded="false" aria-controls="collapse-{{ Str::slug($name) }}">
                                        {{ $name }} ({{ $transactions->count() }} flagged)
                                    </button>
                                </h5>
                            </div>

                            <div id="collapse-{{ Str::slug($name) }}" class="collapse"
                                 aria-labelledby="heading-{{ Str::slug($name) }}" data-parent="#fraudAccordion">
                                <div class="card-body bg-secondary p-2">
                                    <table class="table table-sm table-striped mb-0">
                                        <thead>
                                        <tr>
                                            <th>Note</th>
                                            <th>Amount</th>
                                            <th>Timestamp</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        @foreach($transactions as $txn)
                                            <tr>
                                                <td>{{ $txn->note }}</td>
                                                <td>${{ number_format($txn->amount) }}</td>
                                                <td>{{ $txn->timestamp }}</td>
                                            </tr>
                                        @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
@stop