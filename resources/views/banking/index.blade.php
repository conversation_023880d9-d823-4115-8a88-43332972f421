@extends('adminlte.page')

@section('title', 'Banking')

@section('content_header')
    <h1>BLRP Server Administration - Bank Accounts</h1>
@stop

@push('css')
    <style type="text/css">
        #accountsTable tbody tr {
            cursor: pointer;
        }
    </style>
@endpush

@section('content')
    <div class="card">
        <div class="card-body">
            <table class="table table-bordered table-striped table-hover table-sm wrap responsive" id="accountsTable">
                <thead>
                    <th>ID</th>
                    <th>Number</th>
                    <th>Type</th>
                    <th>Name</th>
                    <th>Balance</th>
                    <th>Owner</th>
                    <th>Users</th>
                </thead>

                <tbody></tbody>
            </table>
        </div>
    </div>
@stop

@push('js')
    <script>
        $('#accountsTable').DataTable({
            processing: true,
            serverSide: true,
            ajax: '{!! url('datatable/banking/accounts') !!}',
            autoWidth: false,
            bLengthChange: false, // Remove records per page selector
            order: [
                [1, 'asc']
            ],

            columns: [
                { data: 'id' },
                { data: 'account_number' },
                { data: 'account_type' },
                { data: 'account_name' },
                { data: 'balance' },
                { data: 'owner' },
                { data: 'characters_count' },
            ],

            columnDefs: [
                { width: "5%", targets: 1 },
                { width: "5%", targets: 2 },
                { width: "20%", targets: 3 },
                { width: "25%", targets: 4 },
                { width: "25%", targets: 5 },
                { width: "5%", targets: 6 },

                { visible: false, targets: [0] },
            ],

            fnRowCallback: function(nRow, aData) {
                $(nRow).click(function(e) {
                    window.location.href = 'banking/' + aData.account_number;
                });
            },
        })
    </script>
@endpush
