@extends('adminlte.page')

@section('title', 'Banking')

@section('content_header')
    <h1>BLRP Server Administration - {{ $account->account_type }} Bank Account - {{ $account->account_number }} ({{ $account->account_name }})</h1>
@stop

@push('css')
    <style type="text/css">
        #charactersTable tbody tr {
            cursor: pointer;
        }
    </style>
@endpush

@section('content')
    @if(isset($siphonData))
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col">
                        <div class="card">
                            <div class="card-body">
                                <canvas id="siphon-amounts" width="400" height="50"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif

    @if(isset($casinoData))
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col">
                        <div class="card">
                            <div class="card-body">
                                <canvas id="casino-amounts" width="400" height="150"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif

    <div class="card">
        <div class="card-body">
            <h1>Current Balance: {{ $account->balance_formatted }}</h1>
        </div>
    </div>

    <div class="card">
        <div class="card-body">
            <div class="row">
                <div class="col-9">
                    <table class="table table-bordered table-striped table-hover table-sm wrap responsive" id="transactionsTable">
                        <thead>
                            <th>ID</th>
                            <th>UUID</th>
                            <th>Transactor</th>
                            <th>Type</th>
                            <th>Note</th>
                            <th>Amount</th>
                            <th>Hidden</th>
                            <th>Timestamp</th>
                        </thead>

                        <tbody></tbody>
                    </table>
                </div>

                <div class="col-3">
                    <table class="table table-bordered table-striped table-hover table-sm wrap responsive" id="charactersTable">
                        <thead>
                            <th>ID</th>
                            <th>VRP</th>
                            <th>Name</th>
                            <th>Owner</th>
                        </thead>

                        <tbody></tbody>
                    </table>

                    @can('banking-advanced')
                        <br />
                        <livewire:bank-actions :account="$account" />
                    @endcan
                </div>
            </div>
        </div>
    </div>
@stop

@php
$transaction_colors = [
    'Blackjack - Deposit' => [86, 168, 179, 0.75],
    'Blackjack - Withdrawal' => [86, 168, 179, 0.5],
    'Blackjack - Profit' => [86, 168, 179, 1],
    'Lucky Wheel - Deposit' => [255, 139, 139, 0.75],
    'Lucky Wheel - Withdrawal' => [255, 139, 139, 0.5],
    'Lucky Wheel - Profit' => [255, 139, 139, 1],
    'Roulette - Deposit' => [136, 99, 72, 0.75],
    'Roulette - Withdrawal' => [136, 99, 72, 0.5],
    'Roulette - Profit' => [136, 99, 72, 1],
    'Slots - Deposit' => [250, 205, 82, 0.75],
    'Slots - Withdrawal' => [250, 205, 82, 0.5],
    'Slots - Profit' => [250, 205, 82, 1],
];
@endphp

@push('js')
    <script>
        $('#transactionsTable').DataTable({
            processing: true,
            serverSide: true,
            ajax: '{!! url('datatable/banking/transactions/' . $account->account_number) !!}',
            autoWidth: false,
            bLengthChange: false, // Remove records per page selector
            order: [
                [7, 'desc']
            ],

            columns: [
                { data: 'id' },
                { data: 'transaction_uuid' },
                { data: 'transactor_name' },
                { data: 'transaction_type' },
                { data: 'note' },
                { data: 'amount' },
                { data: 'hidden' },
                { data: 'timestamp' },
            ],

            columnDefs: [
                { width: "15%", targets: 2 },
                { width: "15%", targets: 3 },
                { width: "40%", targets: 4 },
                { width: "5%", targets: 5 },
                { width: "5%", targets: 6 },
                { width: "15%", targets: 7 },

                { visible: false, targets: [0, 1] },
            ],
        })

        $('#charactersTable').DataTable({
            processing: true,
            serverSide: true,
            ajax: '{!! url('datatable/banking/characters/' . $account->account_number) !!}',
            autoWidth: false,
            bLengthChange: false, // Remove records per page selector
            order: [
                [0, 'asc']
            ],

            columns: [
                { data: 'id' },
                { data: 'identifier' },
                { data: 'full_name' },
                { data: 'owner' },
            ],

            columnDefs: [
                { width: "20%", targets: 0 },
                { width: "75%", targets: 2 },
                { width: "5%", targets: 3 },

                { visible: false, targets: [1] },
            ],

            fnRowCallback: function(nRow, aData) {
                $(nRow).click(function(e) {
                    window.location.href = '/player/' + aData.identifier;
                });
            },
        })

        @if(isset($siphonData))
            var context = document.getElementById('siphon-amounts').getContext('2d');
            var siphoneChart = new Chart(context, {
                type: 'line',
                data: {
                    labels: [
                        @foreach($siphonData as $date => $amount)
                        {!! '"' . $date . '",' !!}
                        @endforeach
                    ],
                    datasets: [
                        {
                            label: 'Government Deposits',
                            data: [
                                @foreach($siphonData as $date => $amount)
                                {{ $amount . ',' }}
                                @endforeach
                            ],
                            borderColor: [
                                'rgba(255, 99, 132, 1)',
                            ],
                            backgroundColor: 'rgba(0, 0, 0, 0)',
                            borderWidth: 1,
                            yAxisID: 'siphonamount',
                        },
                    ]
                },
                options: {
                    scales: {
                        yAxes: [
                            {
                                id: 'siphonamount',
                                ticks: {
                                    beginAtZero: true,
                                    callback: function(value) {
                                        return value.toLocaleString("en-US",{style:"currency", currency:"USD"}).replace('.00', '');
                                    }
                                }
                            },
                        ]
                    }
                }
            });
        @endif

        @if(isset($casinoData))
        var context = document.getElementById('casino-amounts').getContext('2d');
        var siphoneChart = new Chart(context, {
            type: 'line',
            data: {
                labels: [
                    @foreach($casinoDates as $date)
                        {!! '"' . $date . '",' !!}
                    @endforeach
                ],
                datasets: [
                    @foreach($casinoData as $game_name => $game_data)
                        @foreach($game_data as $transaction_type => $transaction_data)
                            @php($label = "$game_name - $transaction_type")

                            {
                                label: '{{ $label }}',
                                data: [
                                    @foreach($casinoDates as $date)
                                        @if(array_key_exists($date, $transaction_data))
                                            {{ $transaction_data[$date] . ',' }}
                                        @else
                                            {{ '0,' }}
                                        @endif
                                    @endforeach
                                ],
                                borderColor: [
                                    'rgba({{ $transaction_colors[$label][0] }}, {{ $transaction_colors[$label][1] }}, {{ $transaction_colors[$label][2] }}, {{ $transaction_colors[$label][3] }})',
                                ],
                                backgroundColor: 'rgba(0, 0, 0, 0)',
                                borderWidth: 1,
                                yAxisID: 'main',
                            },
                        @endforeach
                    @endforeach
                ]
            },
            options: {
                scales: {
                    yAxes: [
                        {
                            id: 'main',
                            ticks: {
                                beginAtZero: true,
                                callback: function(value) {
                                    return value.toLocaleString("en-US",{style:"currency", currency:"USD"}).replace('.00', '');
                                }
                            }
                        },
                    ]
                }
            }
        });
        @endif
    </script>
@endpush
