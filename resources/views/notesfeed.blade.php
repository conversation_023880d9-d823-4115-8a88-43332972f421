@extends('adminlte.page')

@section('title', 'Notes Feed')

@section('content_header')
    <h1>BLRP Server Administration - Notes Raw Feed</h1>
@stop

@push('css')
    <style type="text/css">
        #notesFeedTable tbody tr {
            cursor: pointer;
        }
    </style>
@endpush

@section('content')
    <div class="card">
        <div class="card-body">
            <table class="table table-bordered table-striped table-hover table-sm wrap responsive" id="notesFeedTable">
                <thead>
                    <th>VRP ID</th>
                    <th>Added By</th>
                    <th>Node</th>
                    <th>Timestamp</th>
                </thead>

                <tbody></tbody>
            </table>
        </div>
    </div>
@stop

@push('js')
    <script>
        $('#notesFeedTable').DataTable({
            processing: true,
            serverSide: true,
            ajax: '{!! url('datatable/notesfeed') !!}',
            autoWidth: false,
            bLengthChange: false, // Remove records per page selector
            order: [
                [3, 'desc']
            ],

            columns: [
                { data: 'user_id' },
                { data: 'added_by' },
                { data: 'note' },
                { data: 'timestamp' },
            ],

            columnDefs: [
                { width: "5%", targets: 0 },
                { width: "10%", targets: 1 },
                { width: "75%", targets: 2 },
                { width: "10%", targets: 3 },
            ],

            fnRowCallback: function(nRow, aData) {
                $(nRow).click(function(e) {
                    window.location.href = 'player/' + aData.user_id;
                });
            },
        })
    </script>
@endpush
