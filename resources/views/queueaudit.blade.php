@extends('adminlte.page')

@section('title', 'Queued Players')

@section('content_header')
    <h1>BLRP Server Administration - Priority Audit</h1>
@stop

@section('content')
    <div class="card">
        <div class="card-body">
            <table class="table table-bordered table-striped table-hover table-sm wrap responsive" id="queueTable">
                <thead>
                    <th>VRP ID</th>
                    <th>Most Recent Character</th>
                    <th>Priority</th>
                </thead>

                <tbody>
                @forelse($players as $player)
                    <tr>
                        <td>{{ $player->id }}</td>
                        <td>{{ $player?->currentCharacter?->full_name }}</td>
                        <td>{{ config('blrp.priority_levels')[$player->priority] ? str_pad($player->priority, 3, '0', STR_PAD_LEFT) . ' – ' . config('blrp.priority_levels')[$player->priority] : $player->priority }}</td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="4" style="text-align: center">No players with priority</td>
                    </tr>
                @endforelse
                </tbody>
            </table>
        </div>
    </div>
@stop

