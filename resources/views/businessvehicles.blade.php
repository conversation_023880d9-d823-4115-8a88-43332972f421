@extends('adminlte.page')

@section('title', 'Business Vehicles')

@section('content_header')
    <h1>BLRP Server Administration - Business Vehicles</h1>
@stop

@push('css')
    <style type="text/css">
        #tweetsTable tbody tr {
            cursor: pointer;
        }
    </style>
@endpush

@section('content')
    <div class="card">
        <div class="card-body">
            <table class="table table-bordered table-striped table-hover table-sm wrap responsive" id="businessVehiclesTable">
                <thead>
                    <th>Business Name</th>
                    <th>License</th>
                    <th>Model</th>
                    <th>Actions</th>
                </thead>

                <tbody></tbody>
            </table>
        </div>
    </div>
@stop

@push('js')
    <script>
        console.log('here');

        $('#businessVehiclesTable').DataTable({
            processing: true,
            serverSide: true,
            ajax: '{!! url('datatable/businessvehicles') !!}',
            autoWidth: false,
            bLengthChange: false, // Remove records per page selector
            pageLength: 25,
            order: [
                // [4, 'desc']
            ],

            columns: [
                { data: 'business' },
                { data: 'registration' },
                { data: 'vehicle' },
                { data: 'actions' },
            ],

            columnDefs: [
              /*  { width: "5%", targets: 0 },
                { width: "15%", targets: 1 },
                { width: "15%", searchable: false, targets: 2 },
                { width: "20%", targets: 3 },
                { width: "20%", targets: 4 },
                { width: "10%", targets: 5 },

                { orderable: false, targets: [2, 5] },
                { className: 'text-center', targets: [5] }, */
            ],
        })
    </script>
@endpush
