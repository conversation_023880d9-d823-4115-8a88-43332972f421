@extends('adminlte.page')

@section('title', 'Message & Report History')

@section('content_header')
    <h1>BLRP Server Administration - Message & Report History</h1>
@stop

@push('css')
    <style type="text/css">
        #tweetsTable tbody tr {
            cursor: pointer;
        }
    </style>
@endpush

@section('content')
    <div class="card">
        <div class="card-body">
            <table class="table table-bordered table-striped table-hover table-sm wrap responsive" id="dmHistoryTable">
                <thead>
                <th>From</th>
                <th>From (id)</th>
                <th>To</th>
                <th>To (id)</th>
                <th>Message</th>
                <th>Time</th>
                </thead>

                <tbody></tbody>
            </table>
        </div>
    </div>
@stop

@push('js')
    <script>

        $('#dmHistoryTable').DataTable({
            processing: true,
            serverSide: true,
            ajax: '{!! url('datatable/dmhistory') !!}',
            autoWidth: false,
            bLengthChange: false, // Remove records per page selector
            pageLength: 25,
            order: [
                 [5, 'desc']
            ],

            columns: [
                {
                    data: 'from',
                },
                {
                    data: 'from_id',
                    visible: false
                },
                {
                    data: 'to',
                },
                {
                    data: 'to_id',
                    visible: false
                },
                { data: 'message' },
                { data: 'timestamp' }, // Assuming 'time' is the column name for the time information
            ],

            // Add this part for debugging
            initComplete: function(settings, json) {
                console.log('Data loaded:', json);
            }

        })
    </script>
@endpush
