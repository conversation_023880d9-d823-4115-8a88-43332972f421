@extends('adminlte.page')

@section('title', 'Impound History')

@section('content_header')
    <h1>BLRP Server Administration - Impound History</h1>
@stop

@push('css')
    <style type="text/css">
        #tweetsTable tbody tr {
            cursor: pointer;
        }
    </style>
@endpush

@section('content')
    <div class="card">
        <div class="card-body">
            <table class="table table-bordered table-striped table-hover table-sm wrap responsive" id="impoundHistoryTable">
                <thead>
                    <th>Registration</th>
                    <th>Impounded by</th>
                    <th>Impound Length</th>
                    <th>Impound Time</th>
                    <th>Impound Type</th>
                </thead>

                <tbody></tbody>
            </table>
        </div>
    </div>
@stop

@push('js')
    <script>

        $('#impoundHistoryTable').DataTable({
            processing: true,
            serverSide: true,
            ajax: '{!! url('datatable/impoundhistory') !!}',
            autoWidth: false,
            bLengthChange: false, // Remove records per page selector
            pageLength: 25,
            order: [
                [3, 'desc']
            ],

            columns: [
                { data: 'vehicle.registration' },
                {
                    data: 'character.full_name', searchable: false
                },
                { data: 'impnd_length' },
                { data: 'impnd_time' },
                { data: 'impound_type' },
                { data: 'character.firstname', visible: false }, // added for searchability
                { data: 'character.lastname', visible: false }, // added for searchability
            ],

        })
    </script>
@endpush
