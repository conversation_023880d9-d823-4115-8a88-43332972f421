@extends('adminlte.page')

@section('title', 'Twitter Feed')

@section('content_header')
    <h1>BLRP Server Administration - Twitter</h1>
@stop

@push('css')
    <style type="text/css">
        #tweetsTable tbody tr {
            cursor: pointer;
        }
    </style>
@endpush

@section('content')
    <div class="card">
        <div class="card-body">
            <table class="table table-bordered table-striped table-hover table-sm wrap responsive" id="tweetsTable">
                <thead>
                    <th>VRP ID</th>
                    <th>Author Account</th>
                    <th>Author Character</th>
                    <th>Tweet</th>
                    <th>Timestamp</th>
                    <th>Actions</th>
                </thead>

                <tbody></tbody>
            </table>
        </div>
    </div>
@stop

@push('js')
    <script>
        $('#tweetsTable').DataTable({
            processing: true,
            serverSide: true,
            ajax: '{!! url('datatable/tweets') !!}',
            autoWidth: false,
            bLengthChange: false, // Remove records per page selector
            order: [
                [4, 'desc']
            ],

            columns: [
                { data: 'character.identifier' },
                { data: 'owner.username', defaultContent: 'Unknown' },
                { data: null, render: function(data) {
                    return data.character.firstname + ' ' + data.character.lastname;
                  }
                },
                { data: 'message' },
                { data: 'time' },
                { data: 'actions' },
            ],

            columnDefs: [
                { width: "5%", targets: 0 },
                { width: "15%", targets: 1 },
                { width: "15%", searchable: false, targets: 2 },
                { width: "20%", targets: 3 },
                { width: "20%", targets: 4 },
                { width: "10%", targets: 5 },

                { orderable: false, targets: [2, 5] },
                { className: 'text-center', targets: [5] },
            ],

            fnRowCallback: function(nRow, aData) {
                $(nRow).click(function(e) {
                    if(!e.target.className.includes('btn')) {
                        window.location.href = 'player/' + aData.character.identifier;
                    }
                });
            },
        })
    </script>
@endpush
