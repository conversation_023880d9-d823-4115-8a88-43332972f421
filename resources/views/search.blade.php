@extends('adminlte.page')

@section('title', 'Search Results')

@section('content_header')
    <h1>BLRP Server Administration - Player Search</h1>
@stop

@push('css')
    <style type="text/css">
        #searchResultsTable tbody tr {
            cursor: pointer;
        }
    </style>
@endpush

@section('content')
    <div class="card">
        <div class="card-body">
            <table class="table table-bordered table-striped table-hover table-sm wrap responsive" id="searchResultsTable">
                <thead>
                    <th>VRP ID</th>
                    <th>Matched Value</th>
                    <th>Most Recent Character</th>
                    <th>Last IP</th>
                    <th>Last Login</th>
                    <th>Banned</th>
                </thead>

                <tbody>
                    @foreach($results as $result)
                        <tr>
                            @for($i = 0; $i <= 5; $i++)
                                @if($i == 5)
                                    <td>{!! $result[$i] ? '<span class="badge badge-danger">Banned</span>' : '<span class="badge badge-success">Unbanned</span>' !!}</td>
                                @else
                                    <td>{{ $result[$i] }}</td>
                                @endif
                            @endfor
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
@stop

@push('js')
    <script>
        $('#searchResultsTable').DataTable({
            bLengthChange: false, // Remove records per page selector
            pageLength: 20,
            autoWidth: false,
            columnDefs: [
                {width: "5%", targets: 0},
                {width: "20%", targets: 1},
                {width: "20%", targets: 2},
                {width: "20%", targets: 3},
                {width: "10%", targets: 4},
                {width: "5%", targets: 5},
            ],

            fnRowCallback: function(nRow, aData) {
                $(nRow).click(function() {
                    window.location.href = 'player/' + aData[0];
                });
            },
        })
    </script>
@endpush
