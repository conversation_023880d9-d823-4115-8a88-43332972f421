@extends('adminlte.page')

@section('title', 'PLP Requests')

@section('content_header')
    <h1>BLRP Server Administration - Personal License Plate Requests</h1>
@stop

@section('content')
    <div class="card">
        <div class="card-body">
            <table class="table table-bordered table-striped table-hover table-sm wrap responsive" id="plpTable">
                <thead>
                    <th>ID</th>
                    <th>VRP ID</th>
                    <th>Character Name</th>
                    <th>Vehicle Model</th>
                    <th>Current Plate</th>
                    <th>Requested Plate</th>
                    <th>Explanation</th>
                    <th>Processed</th>
                    <th>Approved</th>
                    <th>Processed By</th>
                    <th>Actions</th>
                </thead>

                <tbody></tbody>
            </table>
        </div>
    </div>
@stop

@push('js')
    <script>
        $('#plpTable').DataTable({
            processing: true,
            serverSide: true,
            ajax: {
            url: '/datatable/plps',
                data: function(d) {
                    return {
                    draw: d.draw,
                    start: d.start,
                    length: d.length,
                    search: d.search.value,
                    order_col: d.order[0].column,
                    order_dir: d.order[0].dir,
                    };
                }
            },
            autoWidth: false,
            order: [
                [0, 'asc']
            ],

            columns: [
                { data: 'id' },
                { data: 'user_id' },
                { data: 'character.full_name' },
                { data: 'vehicle.vehicle' },
                { data: 'current_plate' },
                { data: 'requested_plate' },
                { data: 'explanation' },
                { data: 'processed' },
                { data: 'approved' },
                { data: 'processed_by' },
                { data: 'actions' },
            ],

            columnDefs: [
                { width: "5%",  targets: 1 },
                { width: "10%", targets: 2 },
                { width: "10%", targets: 3 },
                { width: "10%", targets: 4 },
                { width: "10%", targets: 5 },
                { width: "20%", targets: 6 },
                { width: "5%",  targets: 7 },
                { width: "5%",  targets: 8 },

                { class: 'text-center', targets: [7, 8, 10] },
                { visible: false, targets: [0] },
                { orderable: false, targets: [7, 8] },
            ],
        })
    </script>
@endpush
