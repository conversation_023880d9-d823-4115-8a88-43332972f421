@extends('adminlte.page')

@section('title', 'LifeInvader Feed')

@section('content_header')
    <h1>BLRP Server Administration - LifeInvader</h1>
@stop

@push('css')
    <style type="text/css">
        #tweetsTable tbody tr {
            cursor: pointer;
        }
    </style>
@endpush

@section('content')
    <div class="card">
        <div class="card-body">
            <table class="table table-bordered table-striped table-hover table-sm wrap responsive" id="lifeInvaderTable">
                <thead>
                    <th>VRP ID</th>
                    <th>Author Character</th>
                    <th>Post</th>
                    <th>Timestamp</th>
                    <th>Actions</th>
                </thead>

                <tbody></tbody>
            </table>
        </div>
    </div>
@stop

@push('js')
    <script>
        console.log('here')
        $('#lifeInvaderTable').DataTable({
            processing: true,
            serverSide: true,
            ajax: '{!! url('datatable/lifeinvader') !!}',
            autoWidth: false,
            bLengthChange: false, // Remove records per page selector
            order: [
               [3, 'desc']
            ],

            columns: [
                { data: 'citizen.vrp_id' },
                { data: 'citizen.name' },
                { data: 'content' },
                { data: 'created_at' },
                { data: 'actions' },
            ],

            columnDefs: [
                { width: "5%", targets: 0 },
                { width: "15%", targets: 1 },
                { width: "15%", targets: 3 },
                { width: "10%", targets: 4 },
                { orderable: false, targets: [4] },
                { className: 'text-center', targets: [4] },
            ],

            fnRowCallback: function(nRow, aData) {
                $(nRow).click(function(e) {
                    if(!e.target.className.includes('btn')) {
                        window.location.href = 'player/' + aData.citizen.vrp_id;
                    }
                });
            },
        })
    </script>
@endpush
