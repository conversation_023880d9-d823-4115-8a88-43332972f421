@extends('adminlte.page')

@section('title', 'Faction Report')

@section('content_header')
    <h1>Faction Bonus Report</h1>
@stop

@section('content')
    <div class="container">
        <h1>Manage Bonuses for Player ID {{ $userId }}</h1>

        <!-- Display Validation Errors -->
        @if ($errors->any())
            <div class="alert alert-danger">
                <ul>
                    @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <!-- Button to Toggle Form Visibility -->
        <button id="toggle-form" class="btn btn-primary">Add New Bonus</button>

        <!-- Form to Add New Bonus -->
        <div id="bonus-form" style="display: none;">
            <form action="{{ route('factionbonuses.add', $userId) }}" method="POST">
                @csrf
                <div class="form-group">
                    <label for="character">Character</label>
                    <select name="character_id" id="character" class="form-control">
                        <option value="">Select Character</option>
                        @foreach ($characters as $character)
                            <option value="{{ $character->id }}">{{ $character->fullname }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="form-group">
                    <label for="expiry_date">Expiry Date</label>
                    <input type="hidden" name="expiry_date" id="expiry_date" class="form-control" required>
                    <input type="text" id="expiry_date_display" class="form-control" required>
                </div>

                <div class="form-group">
                    <label for="extra_pay">Extra Pay</label>
                    <input type="number" name="extra_pay" id="extra_pay" class="form-control" required max="1000">
                </div>
                <div class="form-group">
                    <label for="reason">Reason</label>
                    <input type="text" name="reason" id="reason" class="form-control" required>
                </div>
                <div class="form-group">
                    <label for="group">Group</label>
                    <select name="group" id="group" class="form-control">
                        @foreach ($factions as $factionName => $v2Value)
                            <option value="{{ htmlspecialchars($v2Value) }}">{{ htmlspecialchars($factionName) }}</option>
                        @endforeach
                    </select>
                </div>
                <button type="submit" class="btn btn-primary">Add Bonus</button>
            </form>
        </div>

        <!-- Edit Bonus Modal -->
        <div id="edit-bonus-modal" class="modal fade" tabindex="-1" role="dialog">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Edit Bonus</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <form id="edit-bonus-form" action="{{ route('factionbonuses.update', $userId) }}" method="POST">
                            @csrf
                            @method('PUT')
                            <input type="hidden" name="index" id="edit-bonus-index">
                            <div class="form-group">
                                <label for="edit-character">Character</label>
                                <select name="character_id" id="edit-character" class="form-control">
                                    @foreach ($characters as $character)
                                        <option value="{{ $character->id }}">{{ $character->fullname }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="edit-expiry_date">Expiry Date</label>
                                <input type="hidden" name="expiry_date" id="edit-expiry_date" class="form-control" required>
                                <input type="text" id="edit-expiry_date_display" class="form-control" required>
                            </div>
                            <div class="form-group">
                                <label for="edit-extra_pay">Extra Pay</label>
                                <input type="number" name="extra_pay" id="edit-extra_pay" class="form-control" required max="1000">
                            </div>
                            <div class="form-group">
                                <label for="edit-reason">Reason</label>
                                <input type="text" name="reason" id="edit-reason" class="form-control" required>
                            </div>
                            <div class="form-group">
                                <label for="edit-group">Group</label>
                                <select name="group" id="edit-group" class="form-control">
                                    @foreach ($factions as $factionName => $v2Value)
                                        <option value="{{ htmlspecialchars($v2Value) }}">{{ htmlspecialchars($factionName) }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <button type="submit" class="btn btn-primary">Update Bonus</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <table class="table">
            <thead>
            <tr>
                <th>Character Name</th>
                <th>Expiry Date</th>
                <th>Extra Pay</th>
                <th>Reason</th>
                <th>Group</th>
                <th>Actions</th>
            </tr>
            </thead>
            <tbody>
            @forelse($bonuses as $index => $bonus)
                <tr>
                    <td>{{ $bonus['character_name'] }}</td>
                    <td>{{ $bonus['expiry_date'] ? date('Y-m-d H:i:s', $bonus['expiry_date']) : 'N/A' }}</td>
                    <td>{{ $bonus['extra_pay'] }}</td>
                    <td>{{ $bonus['reason'] }}</td>
                    <td>{{ $bonus['group'] }}</td>
                    <td>
                        <!-- Edit Button -->
                        <button class="btn btn-warning edit-btn" data-index="{{ $index }}" data-bonus="{{ json_encode($bonus) }}">Edit</button>

                        <!-- Delete Button -->
                        <form method="POST" action="{{ route('factionbonuses.delete', [
                                'userId' => $userId,
                                'characterId' => $bonus['character_id'],
                                'expiryDate' => $bonus['expiry_date'],
                                'extraPay' => $bonus['extra_pay'],
                                'reason' => $bonus['reason'],
                                'group' => $bonus['group']
                            ]) }}" style="display:inline;">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger">Delete</button>
                        </form>
                    </td>
                </tr>
            @empty
                <tr>
                    <td colspan="6">No bonuses found.</td>
                </tr>
            @endforelse
            </tbody>
        </table>
    </div>

    @push('css')
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    @endpush

    @push('js')
        <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                flatpickr('#expiry_date_display', {
                    dateFormat: 'Y-m-d',
                    onChange: function(selectedDates, dateStr, instance) {
                        var timestamp = Math.floor(selectedDates[0].getTime() / 1000); // Convert to Unix timestamp
                        document.getElementById('expiry_date').value = timestamp;
                    }
                });
                flatpickr('#edit-expiry_date_display', {
                    dateFormat: 'Y-m-d',
                    onChange: function(selectedDates, dateStr, instance) {
                        var timestamp = Math.floor(selectedDates[0].getTime() / 1000); // Convert to Unix timestamp
                        document.getElementById('edit-expiry_date').value = timestamp;
                    }
                });
            });

            document.addEventListener('DOMContentLoaded', function() {
                document.querySelectorAll('.edit-btn').forEach(function(button) {
                    button.addEventListener('click', function() {
                        var bonus = JSON.parse(this.getAttribute('data-bonus'));
                        var index = this.getAttribute('data-index');

                        // Populate the edit form or modal with the bonus data
                        document.getElementById('edit-bonus-index').value = index;
                        document.getElementById('edit-character').value = bonus.character_id;
                        document.getElementById('edit-expiry_date').value = bonus.expiry_date;
                        document.getElementById('edit-expiry_date_display').value = new Date(bonus.expiry_date * 1000).toISOString().split('T')[0]; // Convert Unix timestamp to date string
                        document.getElementById('edit-extra_pay').value = bonus.extra_pay;
                        document.getElementById('edit-reason').value = bonus.reason;
                        document.getElementById('edit-group').value = bonus.group;

                        // Show the edit modal
                        $('#edit-bonus-modal').modal('show');
                    });
                });
            });


            document.getElementById('toggle-form').addEventListener('click', function() {
                var form = document.getElementById('bonus-form');
                if (form.style.display === 'none') {
                    form.style.display = 'block';
                    this.textContent = 'Hide Form';
                } else {
                    form.style.display = 'none';
                    this.textContent = 'Add New Bonus';
                }
            });
        </script>
    @endpush
@endsection
