<!DOCTYPE html>
<html>
    <head>
        <title>CAD / Radios - BLRP Dispatch</title>

        <link rel="stylesheet" href="/disp/style.css?v=1.2"/>
        <link href="/disp/fontawesome/css/fontawesome.css" rel="stylesheet">
        <link href="/disp/fontawesome/css/all.css" rel="stylesheet">

        <!-- TODO: local versions of packages -->
        <script src="https://js.pusher.com/8.2.0/pusher.min.js"></script>
        <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
        <script src="https://unpkg.com/mitt/dist/mitt.umd.js"></script>

        <script type="module">
            import { UnmarshalBoardSync } from "/disp/components/marshal.js";

            let soundErtt = new Audio('/disp/sounds/whiz_ertt.mp3');
            let soundRtt = new Audio('/disp/sounds/whiz_rtt.mp3');

            soundErtt.loop = true;

            import Board from '/disp/components/board.js'
            import Channel from '/disp/components/channel.js?v=3.1'
            import Cli from '/disp/components/cli.js'
            import Ied from '/disp/components/ied.js'
            import Mail from '/disp/components/mail.js'
            import Mailitem from '/disp/components/mailitem.js'

            const app = Vue.createApp({
                data() {
                    return {
                        connected: false,
                        user: { id: 0, name: '' },
                        emitter: null,
                        eventsPending: [],
                        channels: [],
                        timeInterval: null,
                        time: new Date().toLocaleTimeString('en-US', {hour12: false}),
                        commandError: null,
                        lastRttSound: 0,
                        mailItems: [],
                        environment: false,

                        /* Pusher Object */
                        pusher: null,
                        pusherAuthenticationStatus: null,

                        /* Readonly Status */
                        readonly: true,
                        readonlyRadio: true,
                        readonlyCad: true,

                        /* Board Data */
                        boardRaw: null,

                        /* Dispatch Control Channel */
                        dispatchControlChannel: null,
                        dispatchControlStatus: false,
                    }
                },

                methods: {
                    getCookie(id) {
                        let value = document.cookie.match('(^|;)?' + id + '=([^;]*)(;|$)');
                        return value ? decodeURI(value[2]) : null;
                    },

                    computeEmergency() {
                        if (this.eventsPending.filter(e => e.emergency && !e.accepted).length > 0) {
                            soundErtt.play();
                        } else {
                            soundErtt.pause();
                        }
                    },

                    /* Subscribe to radio channel and bind to relevant events */
                    subscribeToRadio() {
                        let radio_channel = root.pusher.subscribe('private-radio');

                        // On successful subscription to private radio channel, set readonly attribute
                        radio_channel.bind('pusher:subscription_succeeded', () => {
                            root.connected = true;
                            root.readonlyRadio = root.readonly;
                        });

                        // IED select (click) event
                        root.emitter.on('EventSelected', event => {
                            root.eventsPending = root.eventsPending.map(e => {
                                return {...e, selected: e.uid === event.uid};
                            });
                        });

                        // IED accept event
                        root.emitter.on('EventAccepted', (event) => {
                            if (!event) {
                                event = root.eventsPending.find(e => e.selected);
                            }

                            if (!event || event.accepted) {
                                return;
                            }

                            fetch("/dispatch/api/rttaccept", {
                                method: "POST",
                                headers: {"Content-Type": "application/json"},
                                body: JSON.stringify({id: root.user.id, uid: event.uid, channel: event.channel})
                            });
                        });

                        root.emitter.on('EventDeleted', () => {
                            let event = root.eventsPending.find(e => e.selected);

                            if (!event.accepted) {
                                return
                            }

                            root.emitter.emit('CloseChannel', {channel: event.channel})
                            root.eventsPending = root.eventsPending.filter(e => e.uid !== event.uid);
                        });

                        let event_types = ['PTTStart', 'PTTEnd', 'RTT', 'RTTAck', 'RTTAccept', 'ManipulateRadio', 'FRC']

                        event_types.forEach((type) => {
                            radio_channel.bind(type, function (data) {
                                let extendedType = type === 'FRC' ? 'FRC' : (type === 'RTT' ? (data.emergency ? 'ERTT' : 'RTT') : (type === 'PTTStart' ? 'PTT' : ''));

                                if (type === 'RTTAccept') {
                                    if (data.id === root.user.id) {
                                        root.eventsPending = root.eventsPending.filter(e => !e.accepted).map(e => {
                                            return e.uid === data.uid ? {...e, accepted: true} : e;
                                        });

                                        root.emitter.emit('OpenChannel', {channel: data.channel})
                                    } else {
                                        root.eventsPending = root.eventsPending.filter(e => e.uid !== data.uid);
                                    }

                                    root.computeEmergency();
                                }

                                // Repackage data with extended type and timestamp
                                data = {
                                    ...data, ...{
                                        type: extendedType,
                                        timestamp: root.time,
                                        unix: Math.floor(Date.now() / 1000),
                                    }
                                }

                                if (type === 'RTT') {
                                    if (data.channel > 100) {
                                        return;
                                    }

                                    if(root.channels.filter(channel => channel.talkgroup === data.channel).length <= 0) {
                                        return;
                                    }

                                    fetch("/dispatch/api/rttack", {
                                        method: "POST",
                                        headers: {"Content-Type": "application/json"},
                                        body: JSON.stringify({id: root.user.id, uid: data.uid, channel: data.channel})
                                    });

                                    // Play RTT sound
                                    if(!data.emergency && soundRtt.paused) {
                                        soundRtt.play();
                                        root.lastRttSound = Math.floor(Date.now() / 1000);
                                    }

                                    root.eventsPending.push(data);
                                    root.computeEmergency();
                                }

                                root.emitter.emit(type, data);
                            });
                        });
                    },

                    subscribeToDispatch() {
                        let dispatch_channel = root.pusher.subscribe('private-dispatch');

                        dispatch_channel.bind('pusher:subscription_succeeded', () => {
                            root.readonlyCad = root.readonly;

                            fetch("/dispatch/api/requestboard", {
                                method: "POST",
                                headers: {"Content-Type": "application/json"},
                                body: JSON.stringify({})
                            });
                        })

                        dispatch_channel.bind('BoardSync', function(data) {
                            root.boardRaw = UnmarshalBoardSync(data);
                            root.emitter.emit('BoardSync');
                        })
                    },

                    subscribeToDispatchControl() {
                        root.dispatchControlChannel = root.pusher.subscribe('presence-dispatch-control');

                        root.dispatchControlChannel.bind('pusher:subscription_succeeded', () => {
                            root.dispatchControlStatus = true;
                        });
                    },
                },

                computed: {
                    authentication() {
                        if(!this.pusher) {
                            return 'Awaiting WebSocket connection...';
                        }

                        if(!this.pusherAuthenticationStatus === null) {
                            return 'Awaiting WebSocket authentication...';
                        }

                        if(this.pusherAuthenticationStatus === false) {
                            return 'Error authenticating with WebSocket';
                        }

                        if(this.user.name === "") {
                            return '';
                        }

                        return `Authenticated as ${ this.user.name }`
                    },
                },

                beforeDestroy() {
                    clearInterval(this.timeInterval);
                },

                mounted() {
                    this.timeInterval = setInterval(() => {
                        root.time = new Date().toLocaleTimeString('en-US', {hour12: false});

                        // Play RTT sound if pending RTT and not answered in 15 seconds
                        if(root.eventsPending.filter(ev => !ev.accepted && !ev.emergency).length > 0 && Math.floor(Date.now() / 1000) - root.lastRttSound > 15 && soundRtt.paused) {
                            soundRtt.play();
                            root.lastRttSound = Math.floor(Date.now() / 1000);
                        }
                    }, 1000);

                    // Capture caps lock key for PTT events
                    window.addEventListener('keydown', function (e) {
                        if (e.key === 'CapsLock' && !root.forcePtt) {
                            if(root.readonly) {
                                return;
                            }

                            let selected_channel = root.channels.filter(channel => channel.selected);

                            if(selected_channel.length === 0) {
                                return;
                            }

                            root.forcePtt = true;

                            fetch("/dispatch/api/manipulateradio", {
                                method: "POST",
                                headers: {"Content-Type": "application/json"},
                                body: JSON.stringify({id: root.user.id, action: 'ForcePTTStart', channel: selected_channel[0].talkgroup})
                            });
                        }
                    });

                    window.addEventListener('keyup', function (e) {
                        if (e.key === 'CapsLock' && root.forcePtt) {
                            root.forcePtt = false;

                            let selected_channel = root.channels.filter(channel => channel.selected);

                            if(selected_channel.length === 0) {
                                return;
                            }

                            fetch("/dispatch/api/manipulateradio", {
                                method: "POST",
                                headers: {"Content-Type": "application/json"},
                                body: JSON.stringify({id: root.user.id, action: 'ForcePTTEnd', channel: selected_channel[0].talkgroup})
                            });
                        }
                    });
                },

                beforeMount() {
                    this.emitter = window.mitt()

                    // Pusher.logToConsole = true; // TODO: remove - dev

                    fetch("/dispatch/api/auth", {
                        method: "POST",
                        headers: {"Content-Type": "application/json"},
                        body: JSON.stringify({})
                    }).then(response => response.json()).then(response => {
                        if(!response.success) {
                            // TODO: redirect
                            return;
                        }

                        root.user = response.user;
                        root.environment = response.environment;
                        root.readonly = response.readonly;

                        // Connect to WebSocket service and authenticate on radio channel
                        root.pusher = new Pusher(response.pusher_key, {
                            cluster: 'mt1',
                            userAuthentication: {
                                endpoint: '/dispatch/api/auth-pusher',
                                headers: {
                                    'X-CSRF-TOKEN': root.getCookie('XSRF-TOKEN'),
                                },
                            },
                            channelAuthorization: {
                                endpoint: "/broadcasting/auth",
                            },
                        });

                        root.pusher.bind('pusher:signin_success', () => {
                            root.pusherAuthenticationStatus = true;
                            root.subscribeToRadio();
                            root.subscribeToDispatch();
                            root.subscribeToDispatchControl();
                        });

                        root.pusher.bind('pusher:error', () => {
                            root.pusherAuthenticationStatus = false;
                        });

                        root.pusher.signin();
                    });
                }
            });

            app
                .component('Board', Board)
                .component('Channel', Channel)
                .component('Cli', Cli)
                .component('Ied', Ied)
                .component('Mail', Mail)
                .component('Mailitem', Mailitem)

            const root = app.mount('#app');
        </script>
    </head>

    <body>
        <div id="app" @keydown="keyDown">
            <!-- Mail items - absolute, on top of everything else -->
            <template v-if="mailItems.length > 0">
                <Mailitem
                    v-for="item in mailItems"
                    :item="item"
                    :class="{ qpers: item.type === 'person', mugshot: item.type === 'mugshot' }"
                />
            </template>

            <!-- Upper -->
            <div id="upper">
                <!-- Left - CAD -->
                <div id="cad">
                    <div id="view">
                        <Board></Board>
                        <Mail></Mail>
                    </div>

                    <!-- Upper - command line -->
                    <Cli></Cli>
                </div>

                <!-- Right - radios -->
                <div id="radios">
                    <div id="radios-upper">
                        <div id="radios-left">
                            <!-- Primary Dispatch Channels -->
                            <Channel name="10 D South" :talkgroup="10" color="rgb(0, 8, 121)"></Channel>
                            <Channel name="11 D North" :talkgroup="11" color="rgb(0, 8, 121)"></Channel>
                            <Channel name="30 EMS D 1" :talkgroup="30" color="rgb(131, 33, 55)"></Channel>

                            <!-- LEO Ops Channels -->
                            <Channel name="12 Ops 1" :talkgroup="12" :collapsible="true" :collapsed="true" color="rgb(0, 8, 121)"></Channel>
                            <Channel name="13 Ops 2" :talkgroup="13" :collapsible="true" :collapsed="true" color="rgb(0, 8, 121)"></Channel>
                            <Channel name="14 Ops 3" :talkgroup="14" :collapsible="true" :collapsed="true" color="rgb(0, 8, 121)"></Channel>
                            <Channel name="15 Ops 4" :talkgroup="15" :collapsible="true" :collapsed="true" color="rgb(0, 8, 121)"></Channel>
                            <Channel name="16 Ops 5" :talkgroup="16" :collapsible="true" :collapsed="true" color="rgb(0, 8, 121)"></Channel>
                            <Channel name="17 Ops 6" :talkgroup="17" :collapsible="true" :collapsed="true" color="rgb(0, 8, 121)"></Channel>
                            <Channel name="18 Ops 7 TMU" :talkgroup="18" :collapsible="true" :collapsed="true" color="rgb(0, 8, 121)"></Channel>
                            <Channel name="19 Ops 8 TMU" :talkgroup="19" :collapsible="true" :collapsed="true" color="rgb(0, 8, 121)"></Channel>

                            <!-- LEO Certifications -->
                            <Channel name="20 INV" :talkgroup="20" :collapsible="true" :collapsed="true" color="rgb(0, 8, 121)"></Channel>
                            <Channel name="21 INV VP+" :talkgroup="21" :collapsible="true" :collapsed="true" color="rgb(0, 8, 121)"></Channel>
                            <Channel name="22 SWAT" :talkgroup="22" :collapsible="true" :collapsed="true" color="rgb(0, 8, 121)"></Channel>
                            <Channel name="23 SWAT VP+" :talkgroup="23" :collapsible="true" :collapsed="true" color="rgb(0, 8, 121)"></Channel>
                            <Channel name="24 GTF" :talkgroup="24" :collapsible="true" :collapsed="true" color="rgb(0, 8, 121)"></Channel>
                            <Channel name="25 GTF VP+" :talkgroup="25" :collapsible="true" :collapsed="true" color="rgb(0, 8, 121)"></Channel>
                            <Channel name="26 CRU" :talkgroup="26" :collapsible="true" :collapsed="true" color="rgb(0, 8, 121)"></Channel>
                            <Channel name="27 CRU VP+" :talkgroup="27" :collapsible="true" :collapsed="true" color="rgb(0, 8, 121)"></Channel>
                            <Channel name="28 Ranger" :talkgroup="28" :collapsible="true" :collapsed="true" color="rgb(0, 8, 121)"></Channel>

                            <!-- EMS -->
                            <Channel name="31 EMS D 2" :talkgroup="31" :collapsible="true" :collapsed="true" color="rgb(131, 33, 55)"></Channel>
                            <Channel name="32 EMS D 3" :talkgroup="32" :collapsible="true" :collapsed="true" color="rgb(131, 33, 55)"></Channel>
                            <Channel name="33 EMS Ops 1" :talkgroup="33" :collapsible="true" :collapsed="true" color="rgb(131, 33, 55)"></Channel>
                            <Channel name="34 EMS Ops 2" :talkgroup="34" :collapsible="true" :collapsed="true" color="rgb(131, 33, 55)"></Channel>

                            <!-- DOC -->
                            <Channel name="40 DOC Ops 1" :talkgroup="40" :collapsible="true" :collapsed="true" color="rgb(84, 104, 111)"></Channel>
                            <Channel name="41 DOC Ops 2" :talkgroup="41" :collapsible="true" :collapsed="true" color="rgb(84, 104, 111)"></Channel>
                            <Channel name="42 DOC Ops 3" :talkgroup="42" :collapsible="true" :collapsed="true" color="rgb(84, 104, 111)"></Channel>
                        </div>

                        <div id="radios-right">
                            <!-- Globals -->
                            <Channel name="LEO Global" :talkgroup="2" :collapsible="true" :collapsed="true" color="rgb(0, 8, 121)"></Channel>
                            <Channel name="EMS Global" :talkgroup="3" :collapsible="true" :collapsed="true" color="rgb(131, 33, 55)"></Channel>
                            <Channel name="DOC Global" :talkgroup="1" :collapsible="true" :collapsed="true" color="rgb(84, 104, 111)"></Channel>
                            <Channel name="DOJ Global" :talkgroup="7" :collapsed="true" color="rgb(181, 148, 16)"></Channel>
                            <Channel name="Tow Global" :talkgroup="4" :collapsed="true" color="rgb(179, 0, 119)"></Channel>
                            <Channel name="50 Liaison 1" :talkgroup="50" :collapsible="true" :collapsed="true" color="rgb(0, 132, 0)"></Channel>
                            <Channel name="51 Liaison 2" :talkgroup="51" :collapsible="true" :collapsed="true" color="rgb(0, 132, 0)"></Channel>
                            <Channel name="52 Liaison 3" :talkgroup="52" :collapsible="true" :collapsed="true" color="rgb(0, 132, 0)"></Channel>
                            <Channel name="53 Liaison 4" :talkgroup="53" :collapsible="true" :collapsed="true" color="rgb(0, 132, 0)"></Channel>
                            <Channel name="54 Liaison 5" :talkgroup="54" :collapsible="true" :collapsed="true" color="rgb(0, 132, 0)"></Channel>
                        </div>
                    </div>

                    <div id="radios-lower">
                        <Ied></Ied>
                    </div>
                </div>
            </div>

            <!-- Lower - status strip -->
            <div id="status">
                <div>
                    <img src="/whiz-logo.webp" />
                    <span class="error">{{ commandError }}</span>
                </div>
                <div>
                    <span>{{ authentication }}</span>
                    <span>{{ time }}</span>
                </div>
            </div>
        </div>
    </body>
</html>





