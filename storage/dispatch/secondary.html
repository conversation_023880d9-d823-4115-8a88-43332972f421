<!DOCTYPE html>
<html>
    <head>
        <title>Map / Call Feed - BLRP Dispatch</title>

        <link rel="stylesheet" href="/disp/style.css?v=1.2"/>
        <link href="/disp/fontawesome/css/fontawesome.css" rel="stylesheet">
        <link href="/disp/fontawesome/css/all.css" rel="stylesheet">

        <!-- TODO: local versions of packages -->
        <script src="https://js.pusher.com/8.2.0/pusher.min.js"></script>
        <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
        <script src="https://unpkg.com/mitt/dist/mitt.umd.js"></script>

        <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" integrity="sha512-xodZBNTC5n17Xt2atTPuE1HxjVMSvLVW9ocqUKLsCC5CXdbqCmblAshOMAS6/keqq/sMZMZ19scR4PsZChSR7A==" crossorigin="">
        <script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js" integrity="sha512-XQoYMqMTK8LvdxXYG3nZ448hOEQiglfqkJs1NOQV44cWnUrBc8PkAOcXy20w0vlaXaVUearIOBhiXZ5V3ynxwA==" crossorigin=""></script>

        <style>
            :root {
                --color-lsfd: rgb(224, 50, 50);
                --color-doc: rgb(238, 198, 78);
                --color-lspd: rgb(93, 182, 229);
                --color-sheriff: rgb(12, 123, 86);
                --color-sahp: rgb(160, 160, 160);
                --color-tracked-vehicle: rgb(204, 112, 0);
            }

            #map {
                /* position:absolute; */
                height: 900px; /* ex 100% */
                width: 1400px; /* ex 100% */
            }
            .faction-lsfd {
                color: var(--color-lsfd);
            }

            .tooltip-lsfd {
                background-color: var(--color-lsfd);
            }

            .faction-doc {
                color: var(--color-doc);
            }

            .tooltip-doc {
                background-color: var(--color-doc);
            }

            .faction-lspd {
                color: var(--color-lspd);
            }

            .tooltip-lspd {
                background-color: var(--color-lspd);
            }

            .faction-sheriff {
                color: var(--color-sheriff);
            }

            .tooltip-sheriff {
                background-color: var(--color-sheriff);
            }

            .faction-sahp {
                color: var(--color-sahp);
            }

            .tooltip-sahp {
                background-color: var(--color-sahp);
            }

            .faction-tracker {
                color: var(--color-tracked-vehicle);
            }

            .tooltip-tracker {
                background-color: var(--color-tracked-vehicle);
            }

            .tooltip-dispatch {
                /* background: transparent; */
                /* border: 1px solid red; */
                box-shadow: none;
                margin-top: 18px;
                text-align: center;
                padding: 0 3px;
                border: none;
                font-weight: bold;
            }

            .tooltip-dispatch::before {
                border: none;
            }
        </style>

        <script type="module">
            import Dispatchmap from '/disp/components/map.js';
            import { UnmarshalBoardSync } from "/disp/components/marshal.js";

            const app = Vue.createApp({
                data() {
                    return {
                        connected: false,
                        user: { id: 0, name: '' },
                        emitter: null,
                        timeInterval: null,
                        time: new Date().toLocaleTimeString('en-US', {hour12: false}),
                        environment: false,

                        /* Pusher Object */
                        pusher: null,
                        pusherAuthenticationStatus: null,

                        /* Board Data */
                        boardRaw: null,

                        /* Dispatch Control Channel */
                        dispatchControlChannel: null,
                        dispatchControlStatus: false,
                    }
                },

                methods: {
                    getCookie(id) {
                        let value = document.cookie.match('(^|;)?' + id + '=([^;]*)(;|$)');
                        return value ? decodeURI(value[2]) : null;
                    },

                    subscribeToDispatch() {
                        let dispatch_channel = root.pusher.subscribe('private-dispatch');

                        dispatch_channel.bind('pusher:subscription_succeeded', () => {
                            fetch("/dispatch/api/requestboard", {
                                method: "POST",
                                headers: {"Content-Type": "application/json"},
                                body: JSON.stringify({})
                            });
                        })

                        dispatch_channel.bind('BoardSync', function(data) {
                            root.boardRaw = UnmarshalBoardSync(data);
                            root.emitter.emit('BoardSync');
                        })
                    },

                    subscribeToDispatchControl() {
                        root.dispatchControlChannel = root.pusher.subscribe('presence-dispatch-control');

                        root.dispatchControlChannel.bind('pusher:subscription_succeeded', () => {
                            root.dispatchControlStatus = true;
                        });

                        root.emitter.emit('DispatchControlBound');
                    },
                },

                computed: {
                    authentication() {
                        if(!this.pusher) {
                            return 'Awaiting WebSocket connection...';
                        }

                        if(!this.pusherAuthenticationStatus === null) {
                            return 'Awaiting WebSocket authentication...';
                        }

                        if(this.pusherAuthenticationStatus === false) {
                            return 'Error authenticating with WebSocket';
                        }

                        if(this.user.name === "") {
                            return '';
                        }

                        return `Authenticated as ${ this.user.name }`
                    },
                },

                beforeDestroy() {
                    clearInterval(this.timeInterval);
                },

                mounted() {
                    this.timeInterval = setInterval(() => {
                        root.time = new Date().toLocaleTimeString('en-US', {hour12: false});
                    }, 1000);

                    // TODO: map code here


                },

                beforeMount() {
                    this.emitter = window.mitt()

                    // Pusher.logToConsole = true; // TODO: remove - dev

                    fetch("/dispatch/api/auth", {
                        method: "POST",
                        headers: {"Content-Type": "application/json"},
                        body: JSON.stringify({})
                    }).then(response => response.json()).then(response => {
                        if(!response.success) {
                            // TODO: redirect
                            return;
                        }

                        root.user = response.user;
                        root.environment = response.environment;

                        // Connect to WebSocket service and authenticate on radio channel
                        root.pusher = new Pusher(response.pusher_key, {
                            cluster: 'mt1',
                            userAuthentication: {
                                endpoint: '/dispatch/api/auth-pusher',
                                headers: {
                                    'X-CSRF-TOKEN': root.getCookie('XSRF-TOKEN'),
                                },
                            },
                            channelAuthorization: {
                                endpoint: "/broadcasting/auth",
                            },
                        });

                        root.pusher.bind('pusher:signin_success', () => {
                            root.pusherAuthenticationStatus = true;
                            root.subscribeToDispatch();
                            root.subscribeToDispatchControl();
                        });

                        root.pusher.bind('pusher:error', () => {
                            root.pusherAuthenticationStatus = false;
                        });

                        root.pusher.signin();
                    });
                }
            });

            app
                .component('Dispatchmap', Dispatchmap)

            const root = app.mount('#app');
        </script>
    </head>

    <body>
        <div id="app" @keydown="keyDown">
            <!-- Upper -->
            <div id="upper">
                <Dispatchmap></Dispatchmap>

                <div id="calls">
                    <!-- TODO: calls -->
                </div>
            </div>

            <!-- Lower - status strip -->
            <div id="status">
                <div>
                    <img src="/whiz-logo.webp" />
                </div>
                <div>
                    <span>{{ authentication }}</span>
                    <span>{{ time }}</span>
                </div>
            </div>
        </div>
    </body>
</html>





