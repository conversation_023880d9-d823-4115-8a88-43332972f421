<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\DispatchController;

Route::group(['middleware' => 'web'], function() {
    Route::get('/', [DispatchController::class, 'getPrimary']);
    Route::get('/secondary', [DispatchController::class, 'getSecondary']);

    Route::group(['prefix' => 'api'], function() {
        Route::any('auth', [DispatchController::class, 'postAuthenticate']);
        Route::any('auth-pusher', [DispatchController::class, 'postAuthenticatePusher']);
        Route::post('rttaccept', [DispatchController::class, 'postRttAccept']);
        Route::post('rttack', [DispatchController::class, 'postRttAck']);
        Route::post('manipulateradio', [DispatchController::class, 'postManipulateRadio']);
        Route::post('command', [DispatchController::class, 'postCommand']);
        Route::post('requestboard', [DispatchController::class, 'postRequestBoard']);
    });
});
