<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\ApiController;

Route::get('twitch', [ApiController::class, 'getTwitch'])->middleware(['cors', 'apikey']);

Route::group(['prefix' => 'allowlist', 'middleware' => ['cors', 'apikey']], function() {
    Route::get('discord/{discord}', [ApiController::class, 'getAllowlistInformation']);
    Route::get('questions', [ApiController::class, 'getAllowlistQuestions']);
    Route::get('start/{discord}', [ApiController::class, 'getStartAllowlist']);
    Route::get('cancel/{application}', [ApiController::class, 'getCancelAllowlist']);
    Route::post('submit', [ApiController::class, 'postAllowlist']);
    Route::post('thread', [ApiController::class, 'postThread']);
    Route::post('action', [ApiController::class, 'postActionAllowlist']);
    Route::get('test', function() {
        $application = \App\Models\AllowlistApplication::find(82);

        return response()->json($application->user->identifiers()->where('identifier', 'LIKE', '%discord%')->first()->identifier);
    });
});

Route::get('yellowjacksign', function() {
    \Debugbar::disable();

    $img = request('img');

    return view('yellowjacksign')->withImg($img);
});

Route::get('yellowjacksign_generate', function() {
    \Debugbar::disable();

    $line1 = request()->get('line1') ?? '';
    $line2 = request()->get('line2') ?? '';
    $line3 = request()->get('line3') ?? '';
    $line4 = request()->get('line4') ?? '';


    $font = storage_path('Eurostile.ttf');
    $font_size = 60.0;

    $img = imagecreatetruecolor(1024, 512);

    imagealphablending($img,false);

    $col = imagecolorallocatealpha($img,255,255,255,127);
    imagefilledrectangle($img,0,0,1024,512,$col);

    imagealphablending($img,true);

    /* */

    $black = imagecolorallocate($img, 0, 0, 0);

    $bb_line1 = imagettfbbox($font_size, 0, $font, $line1); $x1 = ceil((1024 - $bb_line1[2]) / 2);
    $bb_line2 = imagettfbbox($font_size, 0, $font, $line2); $x2 = ceil((1024 - $bb_line2[2]) / 2);
    $bb_line3 = imagettfbbox($font_size, 0, $font, $line3); $x3 = ceil((1024 - $bb_line3[2]) / 2);
    $bb_line4 = imagettfbbox($font_size, 0, $font, $line4); $x4 = ceil((1024 - $bb_line4[2]) / 2);

    imagettftext($img, $font_size, 0,  $x1,  70 + $font_size, $black, $font, $line1); imagealphablending($img,true);
    imagettftext($img, $font_size, 0,  $x2,  164 + $font_size, $black, $font, $line2); imagealphablending($img,true);
    imagettftext($img, $font_size, 0,  $x3,  258 + $font_size, $black, $font, $line3); imagealphablending($img,true);
    imagettftext($img, $font_size, 0,  $x4,  352 + $font_size, $black, $font, $line4); imagealphablending($img,true);

    /* */

    ob_start();

    imagealphablending($img,false);
    imagesavealpha($img,true);
    imagepng($img);

    $buffer = ob_get_contents();

    ob_end_clean();

    $image_b64 = base64_encode($buffer);

    imagedestroy($img);

    $curl = curl_init();

    curl_setopt_array($curl, [
        CURLOPT_URL => 'https://api.imgur.com/3/image',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS => [
            'image' => $image_b64,
            'type' => 'base64',
        ],
        CURLOPT_HTTPHEADER => [
            'Authorization: Client-ID f2c6ae5ea8102f3'
        ],
    ]);

    $response = curl_exec($curl);
    $response = json_decode($response);

    curl_close($curl);

    if(!$response->success) {
        return response()->json(['success' => false]);
    }

    return response()->json(['success' => true, 'url' => $response->data->link]);
});
