<?php

use App\Http\Controllers\NameFilterController;
use App\Models\Identifier;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\FactionController;
use App\Http\Controllers\FactionBonusController;
use App\Http\Controllers\PanelController;
use App\Http\Controllers\DatatablesAjaxController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\BankingController;
use App\Http\Controllers\BusinessController;
use App\Http\Controllers\ElectionController;
use App\Http\Controllers\PlpController;
use App\Http\Controllers\OrgChartController;
use Laravel\Socialite\Facades\Socialite;
use Illuminate\Support\Facades\Gate;

Route::get('logout', function() {
    if(auth()->check()) {
        auth()->logout();
    }

    return redirect()->to('discord')->withSuccess('Signed Out');
});

Route::group(['prefix' => 'discord'], function () {
    Route::get('/', function() {
        if(auth()->check()) {
            return redirect()->to('/');
        }

        return view('login');
    })->name('login');

    Route::get('redirect', function() {
        return Socialite::driver('discord')->redirect();
    });

    Route::get('callback', function() {
        $discord_user = Socialite::driver('discord')->user();

        if(!$discord_user || !$discord_user->id) {
            return redirect()->to('discord')->withError('Error authenticating via Discord');
        }

        $identifier = Identifier::where('identifier', 'discord:' . $discord_user->id)->first();

        if(!$identifier) {
            return redirect()->to('discord')->withError('Unable to locate BadlandsRP user account from Discord identifier: ' . $discord_user->id);
        }

        $user = $identifier->user;

        if(!$user) {
            return redirect()->to('discord')->withError('Unable to locate BadlandsRP user account');
        }

        $can_login_panel = Gate::forUser($user)->allows('login-panel');
        $can_login_dispatch = Gate::forUser($user)->allows('login-dispatch');

        if(!$can_login_panel && !$can_login_dispatch) {
            return redirect()->to('discord')->withError('User is not granted access to this resource');
        }

        auth()->login($user, true);

        if(!$can_login_panel) {
            return redirect()->to('/'); // TODO: redirect to dispatch as they don't have panel credentials
        }

        return redirect()->to('/');
    });
});

Route::group(['middleware' => 'auth'], function() {
    Route::get('/', [PanelController::class, 'index']);
    Route::get('player/{player}', [PanelController::class, 'getPlayer']);
    Route::get('ip/{ip}', [PanelController::class, 'getIp']);
    Route::post('search', [PanelController::class, 'postSearch']);

    Route::group(['middleware' => 'gate:banking-basic'], function() {
        Route::group(['prefix' => 'banking'], function() {
            Route::get('/', [BankingController::class, 'getIndex']);
            Route::get('fraudreport', [BankingController::class, 'getFraudReport']);
            Route::get('{account}', [BankingController::class, 'getAccount']);
        });
    });

    Route::get('vehicletransfer', [PanelController::class, 'getVehicleTransfer'])->middleware('gate:vehicle-actions');
    Route::get('impoundhistory', [PanelController::class, 'getImpoundHistory'])->middleware('gate:vehicle-actions');
    Route::get('itemtracking', function() {
        return view('itemtracking');
    });

    Route::group(['middleware' => 'gate:admin-actions'], function() {
        Route::get('/election/create', [ElectionController::class, 'showForm']);
        Route::post('/election/create', [ElectionController::class, 'createElection']);
        Route::get('/election/view', [ElectionController::class, 'showView']);
        Route::post('/election/toggle-open/{id}', [ElectionController::class, 'toggleOpen']);
        Route::get('/election/details/{id}', [ElectionController::class, 'getDetails']);
    });

    Route::group(['middleware' => 'gate:admin-actions', 'prefix' => 'namefilters'], function() {
        Route::get('/', [NameFilterController::class, 'getIndex']);
        Route::post('/', [NameFilterController::class, 'postFilter']);
        Route::get('/delete/{filter}', [NameFilterController::class, 'deleteFilter']);
        Route::get('/create', [NameFilterController::class, 'createFilter']);
        Route::get('/{filter}', [NameFilterController::class, 'getFilter']);
        Route::post('/{filter?}', [NameFilterController::class, 'postFilter']);
    });

    Route::group(['middleware' => 'gate:staff-actions'], function() {
        Route::get('messages/{character}', [PanelController::class, 'getMessages']);
        Route::get('players', [PanelController::class, 'getPlayers']);
        Route::get('twitter', [PanelController::class, 'getTwitter']);
        Route::get('twitter/delete/{tweet}', [PanelController::class, 'deleteTweet']);
        Route::get('lifeinvader', [PanelController::class, 'getLifeinvader']);
        Route::get('lifeinvader/delete/{post}', [PanelController::class, 'deleteLifeinvader']);
        Route::get('vehicle/delete/{vehicle}', [PanelController::class, 'deleteVehicle']);
        Route::get('vehicle/unseize/{vehicle}', [PanelController::class, 'unseizeVehicle']);
        Route::get('bans', [PanelController::class, 'getBans']);
        Route::get('garages', [PanelController::class, 'getGarages']);
        Route::get('notesfeed', [PanelController::class, 'getNotesFeed']);
        Route::get('housing', [PanelController::class, 'getHousing']);
        Route::get('flagged', [PanelController::class, 'getFlagged']);
        Route::get('vehicleadd', [PanelController::class, 'getVehicleAdd'])->middleware('gate:admin-actions');
        Route::get('f6-search', [PanelController::class, 'getF6Search']);
        Route::get('businessvehicles', [PanelController::class, 'getBusinessVehicles']);
        Route::get('dmhistory', [PanelController::class, 'getDmHistory']);
        Route::get('vehicleprices', [PanelController::class, 'getVehiclePrices']);
        Route::get('/org-chart', [OrgChartController::class, 'index'])->name('orgchart');


        Route::group(['prefix' => 'businesses'], function() {
            Route::get('/', [BusinessController::class, 'getIndex']);
            Route::get('{business}', [BusinessController::class, 'getBusiness']);
        });

        Route::group(['prefix' => 'plprequests'], function() {
            Route::get('/', [PlpController::class, 'getIndex']);
            Route::get('accept/{plpRequest}', [PlpController::class, 'getAccept']);
            Route::get('reject/{plpRequest}', [PlpController::class, 'getReject']);
        });

        Route::group(['prefix' => 'queue', 'middleware' => 'gate:admin-actions'], function() {
            Route::get('/', [PanelController::class, 'getQueue']);
            Route::get('audit', [PanelController::class, 'getQueueAudit']);
            Route::get('bump/{vrp_id}', [PanelController::class, 'getQueueBump'])->middleware('gate:game-actions');
        });

        Route::get('splunkdistance', function() {
            return view('splunkdistance');
        });

    });

    // Tebex plate token refund (senior admin only)
    Route::get('tebex/refund-token/{token}', [App\Http\Controllers\TebexController::class, 'refundPlateToken'])->middleware('gate:senior-actions');

    Route::group(['prefix' => 'datatable'], function() {
        Route::get('user-playtime-columns', [DatatablesAjaxController::class, 'getPlaytimeColumns']);
        Route::get('user-playtime/{user}', [DatatablesAjaxController::class, 'getUserPlaytime']);
        Route::get('user-playtime-v2/{user}', [DatatablesAjaxController::class, 'getUserPlaytimev2']);
        Route::get('user-playtime-names/{user}', [DatatablesAjaxController::class, 'getPlaytimeNames']);
        Route::get('character-vehicles/{character}', [DatatablesAjaxController::class, 'getCharacterVehicles'])->middleware('gate:vehicle-actions');
        Route::get('users', [DatatablesAjaxController::class, 'getPanelUsers'])->middleware('gate:users-manage');
        Route::get('tweets', [DatatablesAjaxController::class, 'getTweets'])->middleware('gate:staff-actions');
        Route::get('lifeinvader', [DatatablesAjaxController::class, 'getLifeinvader'])->middleware('gate:staff-actions');
        Route::get('bans', [DatatablesAjaxController::class, 'getBans'])->middleware('gate:staff-actions');
        Route::get('notesfeed', [DatatablesAjaxController::class, 'getNotesFeed'])->middleware('gate:staff-actions');
        Route::get('flagged', [DatatablesAjaxController::class, 'getFlagged'])->middleware('gate:staff-actions');
        Route::get('businesses', [DatatablesAjaxController::class, 'getBusinesses'])->middleware('gate:staff-actions');
        Route::get('banking/accounts', [DatatablesAjaxController::class, 'getBankAccounts'])->middleware('gate:banking-basic');
        Route::get('banking/transactions/{account_number}', [DatatablesAjaxController::class, 'getBankAccountTransactions'])->middleware('gate:banking-basic');
        Route::get('banking/characters/{account_number}', [DatatablesAjaxController::class, 'getBankAccountCharacters'])->middleware('gate:banking-basic');
        Route::get('plps', [DatatablesAjaxController::class, 'getPlps'])->middleware('gate:staff-actions');
        Route::get('businessvehicles', [DatatablesAjaxController::class, 'getBusinessVehicles']);
        Route::get('impoundhistory', [DatatablesAjaxController::class, 'getImpoundHistory']);
        Route::get('dmhistory', [DatatablesAjaxController::class, 'getDmHistory']);
        Route::get('electionoverview', [DatatablesAjaxController::class, 'getElectionOverview']);
    });

    Route::group(['prefix' => 'users', 'middleware' => 'gate:users-manage'], function() {
        Route::get('/', [UserController::class, 'getUsers']);
        Route::get('rekey/{user}', [UserController::class, 'rekeyUser']);
        Route::get('revoke/{user}', [UserController::class, 'revokeUser']);
    });

    Route::get('factionreport/{faction}/{year?}/{month?}', [FactionController::class, 'getReport']);
    Route::get('/report/business/{business}/{year?}/{month?}', [BusinessController::class, 'getBusinessReport']);


    Route::get('factionbonuses/{userId}', [FactionBonusController::class, 'manageBonuses'])->name('factionbonuses.manage');
    Route::post('factionbonuses/add/{userId}', [FactionBonusController::class, 'addBonus'])->name('factionbonuses.add');
    Route::delete('factionbonuses/{userId}/{characterId}/{expiryDate}/{extraPay}/{reason}/{group}',
        [FactionBonusController::class, 'deleteBonus']
    )->name('factionbonuses.delete');
    Route::put('/factionbonuses/update/{userId}', [FactionBonusController::class, 'updateBonus'])->name('factionbonuses.update');

    Route::get('/bonusreport', [FactionBonusController::class, 'showFactionBonuses'])->name('factionbonuses.report');



    Route::get('tokens', function() {
        $tokens = \App\Models\Token::groupBy('token')->selectRaw('token, COUNT(*) as count')->having('count', '>', 1)->get()->pluck('token');

        $tokens = \App\Models\Token::whereHas('user', function($q) {
            $q->where('steam_check_bypass', false);
        })->whereIn('token', $tokens)->get()->toArray();

        foreach($tokens as $token) {
            dump($token);
        }

        return 'Done';
    });

    Route::get('crashmap', function() {
        return view('map');
    });
});

/*
Route::get('crashstats', function() {
    $crashes = [];

    foreach(file('C:\\Users\\<USER>\\Downloads\\1661379050_19491.json') as $line) {
        $line = json_decode($line);
        $data = json_decode($line->result->_raw);

        $datetime = $data->datetime;
        $log = $data->log;

        $success = preg_match('/Player crash disconnect \/ coords = (.*) \/ reason = (.*) \/ game time = (.*)/', $log, $matches);

        if($success && $success > 0) {
            dump($datetime, $log, $matches);

            $crash = $matches[2];

            if(!array_key_exists($crash, $crashes)) {
                $crashes[$crash] = 0;
            }

            $crashes[$crash]++;
        }
    }

    arsort($crashes);

    dump($crashes);
});
*/

Route::get('itemcount', function() {
    $datas = \App\Models\ServerData::
        where('dkey', 'LIKE', 'chest:%')->
        where('dvalue', 'LIKE', '%usb_drive%')->
        where('dvalue', 'NOT LIKE', '%pd_evidence%')->
        where('dvalue', 'NOT LIKE', '%bin2%')->
        where('dvalue', 'NOT LIKE', '%locker_storage%')->
        get();
    //black_panther_hide

    $count = 0;

    foreach($datas as $data) {
        $data = json_decode($data->dvalue, true);

        if(array_key_exists('usb_drive', $data)) {
            $count += $data['usb_drive']['amount'];
        } else {
            foreach($data as $item_id => $item_data) {
                if(str_contains($item_id, 'usb_drive')) {
                    dump($item_id, $item_data['amount']);
                    $count += $item_data['amount'];
                }
            }
            // dd($data);
        }
    }

    dump('count is', $count);
});

Route::get('ipb_member', function() {
    dd(app('ipb_member'));
});

Route::post('rttack', function() {
    if(!request('uid') || !request('channel') || !request('callsign')) {
        return;
    }

    event(new \App\Events\RTTAck(request('uid'), request('channel'), request('callsign')));
});
