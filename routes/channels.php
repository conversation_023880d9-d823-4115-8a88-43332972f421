<?php

use Illuminate\Support\Facades\Broadcast;
use App\Models\User;

/*
|--------------------------------------------------------------------------
| Broadcast Channels
|--------------------------------------------------------------------------
|
| Here you may register all of the event broadcasting channels that your
| application supports. The given channel authorization callbacks are
| used to check if an authenticated user can listen to the channel.
|
*/

Broadcast::channel('App.User.{id}', function (User $user, $id) {
    return $user->id === (int) $id;
});

Broadcast::channel('radio', function(User $user) {
    return $user->can('dispatch-view');
});

Broadcast::channel('dispatch', function(User $user) {
    return $user->can('dispatch-view');
});

Broadcast::channel('dispatch-control', function(User $user) {
    if(!$user->can('dispatch-view')) {
        return false;
    }

    return [
        'id' => $user->id,
    ];
});
