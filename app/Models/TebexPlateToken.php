<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class TebexPlateToken extends Model
{
    protected $table = 'tebex_plate_tokens';

    protected $fillable = [
        'plate_id'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function plateRequest()
    {
        return $this->belongsTo(PlpRequest::class, 'plate_id', 'id');
    }
}
