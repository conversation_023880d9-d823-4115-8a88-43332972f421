<?php

namespace App\Http\Controllers;

use App\Models\TebexPlateToken;
use App\Models\User;
use Illuminate\Http\Request;

class TebexController extends Controller
{
    public function refundPlateToken(TebexPlateToken $token)
    {
        // Check if user has senior admin permissions
        if (!auth()->user()->can('senior-actions')) {
            return redirect()->back()->with('danger', 'You do not have permission to perform this action.');
        }

        // Check if token is actually redeemed (has a plate_id)
        if (!$token->plate_id) {
            return redirect()->back()->with('danger', 'This token has not been redeemed and cannot be refunded.');
        }

        // Store the plate_id for the note before setting it to null
        $plateId = $token->plate_id;
        $transactionId = $token->transaction_id;

        // Get the player for the note
        $player = $token->user;

        // Set plate_id to null to mark as refunded
        $token->update(['plate_id' => null]);

        // Create senior admin level note
        $player->panelNotes()->create([
            'added_by' => auth()->user()->id,
            'note' => "Refunded token (transaction id: {$transactionId}, plate id request: {$plateId})",
            'level' => 5, // Senior admin level
        ]);

        return redirect()->back()->with('success', 'Plate token refunded successfully.');
    }
}
