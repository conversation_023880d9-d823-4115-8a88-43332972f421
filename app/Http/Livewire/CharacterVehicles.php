<?php

namespace App\Http\Livewire;

use App\Models\Character;
use App\Models\Note;
use App\Models\Vehicle;
use Illuminate\Support\Facades\Http;
use Livewire\Component;

class CharacterVehicles extends Component
{
    protected $listeners = [
        'deleteVehicle' => 'deleteVehicle',
        'unseizeVehicle' => 'unseizeVehicle',
        'resetVehicleGarage' => 'resetVehicleGarage',
        'editVehiclePlate' => 'editVehiclePlate',
        'processPlateChange' => 'processPlateChange',
    ];

    public Character $character;

    public function render()
    {
        return view('livewire.character-vehicles');
    }

    public function deleteVehicle(Vehicle $vehicle)
    {
        $vehicle->delete();
        $this->emitTo('panel-flash', 'flash','success', 'Vehicle deleted');
    }

    public function unseizeVehicle(Vehicle $vehicle)
    {
        $vehicle->seized = false;
        $vehicle->save();
        $this->emitTo('panel-flash', 'flash','success', 'Vehicle unseized');
    }

    public function resetVehicleGarage(Vehicle $vehicle)
    {
        if(str_contains($vehicle->storedGarage, 'y')) {
            $vehicle->in_impound = false;
            $vehicle->impound_length = null;
            $vehicle->impound_release_time = null;
        }

        $vehicle->storedGarage = null;
        $vehicle->save();
        $this->emitTo('panel-flash', 'flash','success', 'Stored vehicle location reset');
    }

    public function editVehiclePlate(Vehicle $vehicle)
    {
        if($vehicle->characterNumber != $this->character->id)
        {
            return;
        }

        $vehicle->load('character');

        $data = [
            'id' => $vehicle->id,
            'cid' => $vehicle->characterNumber,
            'owner_id' => $vehicle->character->identifier,
            'owner_name' => $vehicle->character->full_name,
            'current_plate' => $vehicle->registration,
            'model' => strtoupper($vehicle->vehicle),
        ];

        $this->emit('openPlateEditModal', $data);
    }

    public function processPlateChange(Vehicle $vehicle, Character $character, $new_plate)
    {
        if ($character->id != $this->character->id) {
            return;
        }

        $exists = Vehicle::where('registration', $new_plate)->first();

        if (strlen($new_plate) < 1) {
            $this->emitTo('panel-flash', 'flash', 'danger', 'Plate too short');
            return;
        }

        if (strlen($new_plate) > 8) {
            $this->emitTo('panel-flash', 'flash', 'danger', 'Plate too long');
            return;
        }

        if ($exists) {
            $this->emitTo('panel-flash', 'flash', 'danger', 'Plate already in use');
            return;
        }

        if (!auth()->user()->can('plates-manage')) {
            return;
        }

        $old_plate = $vehicle->registration;
        $vehicle->registration = strtoupper($new_plate);
        $vehicle->save();

        try {
            Http::connectTimeout(5)->timeout(5)->withHeaders([
                'X-Authorization' => 'TOKEN ' . config('blrp.server_api_key'),
                'Content-Type' => 'application/json'
            ])->post(config('blrp.server_api_addr') . '/blrp_api/log', [
                'vrp_id' => auth()->user()->id,
                'log_type' => 'ACTION',
                'log' => "Changed vehicle plate (Via Panel) / old_plate = {$old_plate} / new_plate = {$vehicle->registration} / model = {$vehicle->vehicle} / rowID = {$vehicle->id}",
            ]);
        } catch (ConnectionException $e) {
            \Log::error('API Log Connection Error', ['exception' => $e->getMessage()]);
        }

        $note = new Note;
        $note->user_id = $character->user->id;
        $note->added_by = auth()->user()->id;
        $note->note = "Updated plate for vehicle ID {$vehicle->id} from {$old_plate} to {$vehicle->registration}";
        $note->save();

        $this->emitTo('panel-flash', 'flash', 'success', 'Vehicle plate updated');
    }

    public function dehydrate()
    {
        $this->emit('datatableInit', 'vehiclesTable', 'vehiclesTableC' . $this->character->id . app('lifecycle_id'), $this->character->id);
    }
}
