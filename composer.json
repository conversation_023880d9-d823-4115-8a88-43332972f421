{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^7.3|^8.0", "fruitcake/laravel-cors": "^3.0", "guzzlehttp/guzzle": "^7.0.1", "jeroennoten/laravel-adminlte": "^3.15", "kelvinmo/fernet-php": "^1.0", "laravel/framework": "^9.0", "laravel/socialite": "^5.11", "laravel/tinker": "^2.5", "laravel/ui": "^4.2", "livewire/livewire": "^2.10", "myclabs/php-enum": "^1.8", "pusher/pusher-php-server": "^7.2", "rickselby/laravel-gate-cache": "^3.6", "socialiteproviders/discord": "^4.2", "yajra/laravel-datatables-oracle": "^9.19"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.6", "barryvdh/laravel-ide-helper": "^2.12", "spatie/laravel-ignition": "^1.0", "fakerphp/faker": "^1.19", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.2", "nunomaduro/collision": "^6.1", "phpunit/phpunit": "^9.3.3"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "minimum-stability": "dev", "prefer-stable": true}